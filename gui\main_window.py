"""
Main GUI Window for BlackJack Bot ML - Phase 4.

This module implements the primary application window with layout management,
styling, and integration of all GUI components.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from .card_selector import CardSelectorWidget
from .game_display import GameDisplayWidget
from .advice_engine import AdviceEngineGUI
from .config_panel import ConfigPanelWidget
from .game_actions import GameActionsWidget
from .multi_hand_display import MultiHandDisplayWidget


class BlackjackGUI:
    """
    Main GUI application for BlackJack Bot ML.
    
    Provides a comprehensive interface for card selection, ML advice,
    and game state visualization.
    """
    
    def __init__(self):
        """Initialize the main GUI application."""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_style()
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()
        
    def setup_window(self):
        """Configure the main window properties."""
        self.root.title("BlackJack Bot ML - Phase 4 GUI Interface")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
        # Configure window icon and properties
        self.root.resizable(True, True)
        
    def setup_style(self):
        """Configure the application styling and theme."""
        self.style = ttk.Style()
        
        # Configure modern theme
        self.style.theme_use('clam')
        
        # Define color scheme
        self.colors = {
            'primary': '#2E3440',      # Dark blue-gray
            'secondary': '#3B4252',    # Lighter blue-gray
            'accent': '#5E81AC',       # Blue accent
            'success': '#A3BE8C',      # Green
            'warning': '#EBCB8B',      # Yellow
            'danger': '#BF616A',       # Red
            'background': '#ECEFF4',   # Light gray
            'text': '#2E3440',         # Dark text
            'text_light': '#4C566A'    # Light text
        }
        
        # Configure styles
        self.style.configure('Title.TLabel', 
                           font=('Arial', 16, 'bold'),
                           foreground=self.colors['primary'])
        
        self.style.configure('Heading.TLabel',
                           font=('Arial', 12, 'bold'),
                           foreground=self.colors['primary'])
        
        self.style.configure('Card.TButton',
                           font=('Arial', 10, 'bold'),
                           padding=(10, 5))
        
        self.style.configure('Action.TButton',
                           font=('Arial', 11, 'bold'),
                           padding=(15, 8))
        
        # Configure root background
        self.root.configure(bg=self.colors['background'])
        
    def create_widgets(self):
        """Create all GUI widgets and components."""
        # Main container frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Title label
        self.title_label = ttk.Label(
            self.main_frame,
            text="BlackJack Bot ML - AI Strategy Advisor",
            style='Title.TLabel'
        )
        
        # Create main content areas
        self.create_left_panel()
        self.create_center_panel()
        self.create_right_panel()
        self.create_bottom_panel()
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready - Select cards to get AI advice",
            foreground=self.colors['text_light']
        )
        
    def create_left_panel(self):
        """Create the left panel with card selection."""
        self.left_frame = ttk.LabelFrame(
            self.main_frame,
            text="Card Selection",
            padding="10"
        )
        
        # Card selector widget
        self.card_selector = CardSelectorWidget(
            self.left_frame,
            on_selection_change=self.on_card_selection_change
        )
        
    def create_center_panel(self):
        """Create the center panel with game display."""
        self.center_frame = ttk.LabelFrame(
            self.main_frame,
            text="Game State",
            padding="10"
        )
        
        # Create tabbed interface for different display modes
        self.display_notebook = ttk.Notebook(self.center_frame)

        # Traditional card selector tab
        self.card_selector_tab = ttk.Frame(self.display_notebook)
        self.display_notebook.add(self.card_selector_tab, text="Card Selector Mode")

        # Interactive game tab
        self.interactive_tab = ttk.Frame(self.display_notebook)
        self.display_notebook.add(self.interactive_tab, text="Interactive Game Mode")

        # Enhanced multi-hand game display widget (for interactive mode)
        self.multi_hand_display = MultiHandDisplayWidget(self.interactive_tab)

        # Keep original game display for compatibility (for card selector mode)
        self.game_display = GameDisplayWidget(self.card_selector_tab)
        
    def create_right_panel(self):
        """Create the right panel with advice and configuration."""
        self.right_frame = ttk.Frame(self.main_frame)
        
        # Advice engine widget
        self.advice_frame = ttk.LabelFrame(
            self.right_frame,
            text="AI Advice",
            padding="10"
        )
        self.advice_engine = AdviceEngineGUI(
            self.advice_frame,
            on_advice_update=self.on_advice_update
        )
        
        # Configuration panel
        self.config_frame = ttk.LabelFrame(
            self.right_frame,
            text="Settings",
            padding="10"
        )
        self.config_panel = ConfigPanelWidget(
            self.config_frame,
            on_config_change=self.on_config_change
        )

    def create_bottom_panel(self):
        """Create the bottom panel with both card selector and interactive game actions."""
        self.bottom_frame = ttk.LabelFrame(
            self.main_frame,
            text="Game Controls",
            padding="10"
        )

        # Create tabbed interface for different control modes
        self.controls_notebook = ttk.Notebook(self.bottom_frame)

        # Card selector tab (traditional mode)
        self.card_selector_controls_tab = ttk.Frame(self.controls_notebook)
        self.controls_notebook.add(self.card_selector_controls_tab, text="Card Selection")

        # Interactive game actions tab
        self.game_actions_tab = ttk.Frame(self.controls_notebook)
        self.controls_notebook.add(self.game_actions_tab, text="Interactive Game")

        # Game actions widget (for interactive mode)
        self.game_actions = GameActionsWidget(
            self.game_actions_tab,
            on_action_taken=self.on_action_taken,
            on_game_state_change=self.on_game_state_change
        )

        # Add card selector to the card selector tab
        self.card_selector_bottom = ttk.Label(
            self.card_selector_controls_tab,
            text="Use the Card Selector in the left panel to select cards manually.",
            font=('Arial', 10),
            anchor='center'
        )
        
    def setup_layout(self):
        """Configure the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure root grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Configure main frame grid weights
        self.main_frame.grid_rowconfigure(1, weight=1)  # Main content row
        self.main_frame.grid_rowconfigure(2, weight=0)  # Bottom panel row
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=2)
        self.main_frame.grid_columnconfigure(2, weight=1)

        # Title
        self.title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # Main panels
        self.left_frame.grid(row=1, column=0, sticky='nsew', padx=(0, 10))
        self.center_frame.grid(row=1, column=1, sticky='nsew', padx=5)
        self.right_frame.grid(row=1, column=2, sticky='nsew', padx=(10, 0))

        # Center panel layout (tabbed display)
        self.display_notebook.grid(row=0, column=0, sticky='nsew')
        self.center_frame.grid_rowconfigure(0, weight=1)
        self.center_frame.grid_columnconfigure(0, weight=1)

        # Bottom panel
        self.bottom_frame.grid(row=2, column=0, columnspan=3, sticky='ew', pady=(10, 0))

        # Bottom panel layout (tabbed controls)
        self.controls_notebook.grid(row=0, column=0, sticky='ew')
        self.bottom_frame.grid_columnconfigure(0, weight=1)

        # Card selector bottom tab layout
        self.card_selector_bottom.grid(row=0, column=0, sticky='ew', pady=20)
        
        # Right panel layout
        self.right_frame.grid_rowconfigure(0, weight=2)
        self.right_frame.grid_rowconfigure(1, weight=1)
        self.right_frame.grid_columnconfigure(0, weight=1)
        
        self.advice_frame.grid(row=0, column=0, sticky='nsew', pady=(0, 10))
        self.config_frame.grid(row=1, column=0, sticky='nsew')
        
        # Status bar
        self.status_frame.grid(row=3, column=0, columnspan=3, sticky='ew', pady=(20, 0))
        self.status_label.grid(row=0, column=0, sticky='w')

        # Layout child widgets
        self.card_selector.setup_layout()
        self.game_display.setup_layout()
        self.multi_hand_display.setup_layout()
        self.advice_engine.setup_layout()
        self.config_panel.setup_layout()
        self.game_actions.setup_layout()
        
    def setup_bindings(self):
        """Set up event bindings and keyboard shortcuts."""
        # Keyboard shortcuts
        self.root.bind('<Control-r>', lambda e: self.reset_game())
        self.root.bind('<Control-q>', lambda e: self.quit_application())
        self.root.bind('<F1>', lambda e: self.show_help())

        # Tab change events to synchronize display and control modes
        self.display_notebook.bind('<<NotebookTabChanged>>', self.on_display_tab_changed)
        self.controls_notebook.bind('<<NotebookTabChanged>>', self.on_controls_tab_changed)

        # Window close event
        self.root.protocol("WM_DELETE_WINDOW", self.quit_application)
        
    def on_card_selection_change(self, dealer_cards, player_cards):
        """Handle card selection changes."""
        try:
            # Update game display
            self.game_display.update_cards(dealer_cards, player_cards)
            
            # Get AI advice if we have valid cards
            if dealer_cards and player_cards:
                self.advice_engine.get_advice(dealer_cards, player_cards)
                self.update_status("AI advice updated")
            else:
                self.advice_engine.clear_advice()
                self.update_status("Select cards to get AI advice")
                
        except Exception as e:
            self.show_error(f"Error processing card selection: {str(e)}")
            
    def on_advice_update(self, advice_data):
        """Handle advice engine updates."""
        self.update_status(f"Advice: {advice_data.get('action', 'Unknown')}")
        
    def on_config_change(self, config_data):
        """Handle configuration changes."""
        try:
            # Update advice engine with new configuration
            self.advice_engine.update_config(config_data)
            self.update_status("Configuration updated")
        except Exception as e:
            self.show_error(f"Error updating configuration: {str(e)}")

    def on_action_taken(self, action, game_state):
        """Handle interactive game actions."""
        try:
            # Update multi-hand display with new game state
            bet_amounts = {i: self.game_actions.bet_amount for i in range(len(game_state.player_hands))}
            self.multi_hand_display.update_game_state(
                game_state=game_state,
                bet_amounts=bet_amounts,
                insurance_bet=self.game_actions.insurance_bet,
                side_bets=self.game_actions.side_bets
            )

            # Get AI advice for current situation
            if game_state.player_hands and game_state.dealer_hand.cards:
                current_hand = game_state.player_hands[game_state.current_hand_index]
                dealer_upcard = game_state.dealer_hand.cards[0]

                # Convert to card format expected by advice engine
                dealer_cards = [dealer_upcard]
                player_cards = current_hand.cards

                self.advice_engine.get_advice(dealer_cards, player_cards)

            self.update_status(f"Action taken: {action.value}")

        except Exception as e:
            self.show_error(f"Error handling action: {str(e)}")

    def on_game_state_change(self, game_state):
        """Handle game state changes."""
        try:
            # Update displays
            bet_amounts = {i: self.game_actions.bet_amount for i in range(len(game_state.player_hands))}
            self.multi_hand_display.update_game_state(
                game_state=game_state,
                bet_amounts=bet_amounts,
                insurance_bet=self.game_actions.insurance_bet,
                side_bets=self.game_actions.side_bets
            )

            # Update status
            if game_state.game_over:
                self.update_status("Game over - Start new game to continue")
            else:
                current_hand_num = game_state.current_hand_index + 1
                total_hands = len(game_state.player_hands)
                self.update_status(f"Playing hand {current_hand_num} of {total_hands}")

        except Exception as e:
            self.show_error(f"Error updating game state: {str(e)}")

    def on_display_tab_changed(self, event):
        """Handle display tab changes."""
        try:
            selected_tab = self.display_notebook.index(self.display_notebook.select())
            # Synchronize control tabs with display tabs
            self.controls_notebook.select(selected_tab)

            if selected_tab == 0:  # Card Selector Mode
                self.update_status("Card Selector Mode - Select cards manually for AI advice")
            else:  # Interactive Game Mode
                self.update_status("Interactive Game Mode - Play full blackjack games with ML integration")

        except Exception as e:
            self.show_error(f"Error handling display tab change: {str(e)}")

    def on_controls_tab_changed(self, event):
        """Handle controls tab changes."""
        try:
            selected_tab = self.controls_notebook.index(self.controls_notebook.select())
            # Synchronize display tabs with control tabs
            self.display_notebook.select(selected_tab)

            if selected_tab == 0:  # Card Selection
                self.update_status("Card Selection Mode - Use left panel to select cards")
            else:  # Interactive Game
                self.update_status("Interactive Game Mode - Use game action buttons below")

        except Exception as e:
            self.show_error(f"Error handling controls tab change: {str(e)}")

    def reset_game(self):
        """Reset the game to initial state."""
        self.card_selector.reset()
        self.game_display.reset()
        self.multi_hand_display.clear_display()
        self.game_actions.reset_game()
        self.advice_engine.clear_advice()
        self.update_status("Game reset - Select cards to get AI advice or start interactive game")
        
    def update_status(self, message):
        """Update the status bar message."""
        self.status_label.config(text=message)
        
    def show_error(self, message):
        """Show an error message to the user."""
        messagebox.showerror("Error", message)
        self.update_status(f"Error: {message}")
        
    def show_help(self):
        """Show help information."""
        help_text = """
BlackJack Bot ML - Complete GUI Interface Help

🎮 GAME MODES:
• Card Selector Mode: Manual card selection for AI advice
• Interactive Game Mode: Full blackjack gameplay with ML integration

📋 CARD SELECTOR MODE:
- Click rank buttons (A, 2-10, J, Q, K) to select cards
- Choose dealer upcard first, then player cards
- Use Clear buttons to reset selections
- Get instant AI advice with confidence levels

🎯 INTERACTIVE GAME MODE:
- Start new games with configurable bet amounts
- Use Hit, Stand, Double Down, Split buttons
- Insurance betting when dealer shows Ace
- Surrender options (early/late based on rules)
- Side bets: Perfect Pairs and 21+3
- Real-time bankroll management with Kelly Criterion

💰 BANKROLL FEATURES:
- Dynamic bet sizing based on ML model confidence
- Risk of ruin calculations and bankroll preservation
- Session profit/loss tracking
- Betting strategy performance metrics

🤖 AI INTEGRATION:
- Real-time ML advice for all game situations
- Confidence-based bet sizing recommendations
- Evasion strategy integration for detection avoidance
- Multiple AI agent types (Basic Strategy, Personas, RL)

⌨️ KEYBOARD SHORTCUTS:
- Ctrl+R: Reset game
- Ctrl+Q: Quit application
- F1: Show this help

🔧 ADVANCED FEATURES:
- Multi-hand display for split hands
- Complete rule enforcement (H17, DAS, etc.)
- Side bet payout calculations
- Comprehensive game statistics

For detailed documentation, see TRAINING_OPTIONS.md and README.md.
        """
        messagebox.showinfo("BlackJack Bot ML - Help", help_text)
        
    def quit_application(self):
        """Safely quit the application."""
        if messagebox.askokcancel("Quit", "Are you sure you want to quit?"):
            self.root.quit()
            self.root.destroy()
            
    def run(self):
        """Start the GUI application."""
        try:
            self.update_status("BlackJack Bot ML GUI started successfully")
            self.root.mainloop()
        except Exception as e:
            self.show_error(f"Application error: {str(e)}")
            
    def get_root(self):
        """Get the root window for testing purposes."""
        return self.root


if __name__ == "__main__":
    app = BlackjackGUI()
    app.run()
