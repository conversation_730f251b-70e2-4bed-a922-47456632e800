"""
Reinforcement Learning Environment for BlackJack Bot ML.

This module provides the RL environment wrapper for blackjack training
with evasion considerations and persona integration.
"""

import numpy as np
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum

from core.game_logic import Black<PERSON><PERSON><PERSON>, GameAction, GameState
from core.hand import Hand
from core.game_rules import BlackjackRules, get_training_rules
from personas.base_persona import Base<PERSON>erson<PERSON>, DecisionContext


class RLRewardType(Enum):
    """Types of rewards in the RL environment."""
    GAME_OUTCOME = "game_outcome"
    DETECTION_PENALTY = "detection_penalty"
    PERSONA_BONUS = "persona_bonus"
    CONSISTENCY_PENALTY = "consistency_penalty"
    EXPLORATION_BONUS = "exploration_bonus"


@dataclass
class RLReward:
    """Reward structure for RL training."""
    total: float
    components: Dict[RLRewardType, float]
    
    def __post_init__(self):
        """Ensure total matches sum of components."""
        if self.components:
            calculated_total = sum(self.components.values())
            if abs(self.total - calculated_total) > 1e-6:
                self.total = calculated_total


@dataclass
class RLState:
    """State representation for RL agent."""
    # Player hand information
    player_value: int
    player_soft: bool
    player_pairs: bool
    player_can_double: bool
    player_can_split: bool
    
    # Dealer information
    dealer_upcard: int
    dealer_soft: bool
    
    # Game context
    deck_penetration: float
    bet_amount: float
    
    # Persona context (if available)
    persona_context: Optional[str] = None
    decision_context: Optional[str] = None
    
    # Evasion context
    consistency_score: float = 0.0
    hands_since_switch: int = 0
    
    def to_array(self) -> np.ndarray:
        """Convert state to numpy array for neural network input."""
        # Basic game state (11 features)
        basic_features = np.array([
            self.player_value / 21.0,  # Normalized player value
            float(self.player_soft),
            float(self.player_pairs),
            float(self.player_can_double),
            float(self.player_can_split),
            self.dealer_upcard / 11.0,  # Normalized dealer upcard
            float(self.dealer_soft),
            self.deck_penetration,
            self.bet_amount / 100.0,  # Normalized bet amount
            self.consistency_score,
            self.hands_since_switch / 100.0  # Normalized hands since switch
        ])
        
        # Persona context encoding (3 features)
        persona_features = np.zeros(3)
        if self.persona_context:
            if self.persona_context == "cautious":
                persona_features[0] = 1.0
            elif self.persona_context == "aggressive":
                persona_features[1] = 1.0
            elif self.persona_context == "intuitive":
                persona_features[2] = 1.0
        
        # Decision context encoding (4 features)
        context_features = np.zeros(4)
        if self.decision_context:
            if self.decision_context == "pressure":
                context_features[0] = 1.0
            elif self.decision_context == "confident":
                context_features[1] = 1.0
            elif self.decision_context == "tired":
                context_features[2] = 1.0
            elif self.decision_context == "distracted":
                context_features[3] = 1.0
        
        # Combine all features (18 total)
        return np.concatenate([basic_features, persona_features, context_features])
    
    @property
    def feature_size(self) -> int:
        """Get the size of the feature vector."""
        return 18


class BlackjackRLEnvironment:
    """
    Reinforcement Learning environment for blackjack training.
    
    Provides standardized interface for RL agents to interact with
    blackjack game while considering evasion and persona factors.
    """
    
    def __init__(self, rules: Optional[BlackjackRules] = None,
                 persona: Optional[BasePersona] = None,
                 detection_penalty: float = -10.0, consistency_threshold: float = 0.95):
        """
        Initialize RL environment.

        Args:
            rules: Game rules configuration (default: training-optimized rules)
            persona: Optional persona for behavioral context
            detection_penalty: Penalty for high consistency (detection risk)
            consistency_threshold: Threshold for detection penalty
        """
        self.rules = rules if rules is not None else get_training_rules()
        self.game = BlackjackGame(rules=self.rules)
        self.persona = persona
        self.detection_penalty = detection_penalty
        self.consistency_threshold = consistency_threshold
        
        # State tracking
        self.current_state = None
        self.game_state = None
        self.episode_step = 0
        self.total_reward = 0.0
        self._done = False  # Track episode completion
        
        # Evasion tracking
        self.decision_history = []
        self.consistency_scores = []
        self.hands_since_switch = 0
        
        # Episode statistics
        self.episode_stats = {
            'hands_played': 0,
            'total_reward': 0.0,
            'game_rewards': 0.0,
            'detection_penalties': 0.0,
            'persona_bonuses': 0.0
        }
    
    def reset(self, bet_amount: float = 10.0) -> RLState:
        """
        Reset environment for new episode.
        
        Args:
            bet_amount: Bet amount for the hand
            
        Returns:
            Initial state
        """
        # Start new game
        self.game_state = self.game.start_new_game(bet_amount)
        
        # Reset episode tracking
        self.episode_step = 0
        self.total_reward = 0.0
        self._done = False  # Reset done state
        self.episode_stats = {
            'hands_played': 0,
            'total_reward': 0.0,
            'game_rewards': 0.0,
            'detection_penalties': 0.0,
            'persona_bonuses': 0.0
        }

        # Get initial state
        self.current_state = self._get_state_representation()

        return self.current_state
    
    def step(self, action: GameAction) -> Tuple[RLState, RLReward, bool, Dict[str, Any]]:
        """
        Take action in environment.
        
        Args:
            action: Action to take
            
        Returns:
            Tuple of (next_state, reward, done, info)
        """
        # Validate action
        available_actions = self.game.get_available_actions()
        if action not in available_actions:
            # Invalid action penalty
            reward = RLReward(
                total=-5.0,
                components={RLRewardType.GAME_OUTCOME: -5.0}
            )
            return self.current_state, reward, True, {'invalid_action': True}
        
        # Take action
        self.game_state = self.game.take_action(action)
        self.episode_step += 1
        
        # Check if game is over
        done = self.game.is_game_over()
        self._done = done  # Update internal done state

        # Calculate reward
        reward = self._calculate_reward(action, done)
        
        # Update state
        next_state = self._get_state_representation()
        self.current_state = next_state
        
        # Update statistics
        self.total_reward += reward.total
        self.episode_stats['total_reward'] += reward.total
        for reward_type, value in reward.components.items():
            if reward_type == RLRewardType.GAME_OUTCOME:
                self.episode_stats['game_rewards'] += value
            elif reward_type == RLRewardType.DETECTION_PENALTY:
                self.episode_stats['detection_penalties'] += value
            elif reward_type == RLRewardType.PERSONA_BONUS:
                self.episode_stats['persona_bonuses'] += value
        
        # Prepare info
        info = {
            'episode_step': self.episode_step,
            'game_state': self.game_state,
            'available_actions': self.game.get_available_actions() if not done else [],
            'episode_stats': self.episode_stats.copy()
        }
        
        if done:
            self.episode_stats['hands_played'] += 1
            info['final_results'] = self.game_state.results
        
        return next_state, reward, done, info

    @property
    def done(self) -> bool:
        """Check if the current episode is done."""
        return self._done

    def _get_state_representation(self) -> RLState:
        """Get current state representation."""
        if not self.game_state or not self.game_state.player_hands:
            # Default state for initialization
            return RLState(
                player_value=0,
                player_soft=False,
                player_pairs=False,
                player_can_double=False,
                player_can_split=False,
                dealer_upcard=0,
                dealer_soft=False,
                deck_penetration=0.0,
                bet_amount=10.0
            )
        
        # Get player hand (assume single hand for now)
        player_hand = self.game_state.player_hands[0]
        
        # Get dealer information
        dealer_upcard = 0
        dealer_soft = False
        if self.game_state.dealer_hand.cards:
            dealer_upcard = self.game_state.dealer_hand.cards[0].get_value()
            if self.game_state.dealer_hand.cards[0].is_ace:
                dealer_upcard = 11
            dealer_soft = self.game_state.dealer_hand.is_soft()
        
        # Get game context
        deck_penetration = self.game.deck.get_penetration()
        bet_amount = self.game_state.bet_amount
        
        # Get persona context
        persona_context = None
        decision_context = None
        if self.persona:
            persona_context = self.persona.config.name.lower().split()[0]  # Extract first word
            decision_context = self.persona.current_context.value
        
        # Get evasion context
        consistency_score = self._calculate_current_consistency()
        
        return RLState(
            player_value=player_hand.get_value(),
            player_soft=player_hand.is_soft(),
            player_pairs=player_hand.is_pair(),
            player_can_double=len(self.game_state.can_double) > 0 and self.game_state.can_double[0],
            player_can_split=len(self.game_state.can_split) > 0 and self.game_state.can_split[0],
            dealer_upcard=dealer_upcard,
            dealer_soft=dealer_soft,
            deck_penetration=deck_penetration,
            bet_amount=bet_amount,
            persona_context=persona_context,
            decision_context=decision_context,
            consistency_score=consistency_score,
            hands_since_switch=self.hands_since_switch
        )
    
    def _calculate_reward(self, action: GameAction, done: bool) -> RLReward:
        """Calculate reward for the action taken."""
        reward_components = {}
        
        # Game outcome reward
        if done and self.game_state.results:
            game_reward = self._calculate_game_reward()
            reward_components[RLRewardType.GAME_OUTCOME] = game_reward
        else:
            # Small negative reward for each step to encourage efficiency
            reward_components[RLRewardType.GAME_OUTCOME] = -0.01
        
        # Detection penalty
        consistency_score = self._calculate_current_consistency()
        if consistency_score >= self.consistency_threshold:
            detection_penalty = self.detection_penalty * (consistency_score - self.consistency_threshold)
            reward_components[RLRewardType.DETECTION_PENALTY] = detection_penalty
        
        # Persona bonus (if action aligns with persona)
        if self.persona:
            persona_bonus = self._calculate_persona_bonus(action)
            if persona_bonus != 0:
                reward_components[RLRewardType.PERSONA_BONUS] = persona_bonus
        
        # Exploration bonus (for trying different actions)
        exploration_bonus = self._calculate_exploration_bonus(action)
        if exploration_bonus != 0:
            reward_components[RLRewardType.EXPLORATION_BONUS] = exploration_bonus
        
        return RLReward(
            total=sum(reward_components.values()),
            components=reward_components
        )
    
    def _calculate_game_reward(self) -> float:
        """Calculate reward based on game outcome."""
        if not self.game_state.results:
            return 0.0
        
        result = self.game_state.results[0]  # Assume single hand
        
        if result == "blackjack":
            return 15.0  # 1.5x payout
        elif result == "win":
            return 10.0  # 1x payout
        elif result == "push":
            return 0.0   # No change
        elif result == "loss":
            return -10.0 # Lose bet
        elif result == "bust":
            return -10.0 # Lose bet
        else:
            return 0.0
    
    def _calculate_persona_bonus(self, action: GameAction) -> float:
        """Calculate bonus for persona-aligned behavior."""
        if not self.persona:
            return 0.0
        
        # Get persona recommendation
        try:
            persona_action = self.persona.get_action(self.game_state, 0)
            if action == persona_action:
                return 1.0  # Small bonus for persona alignment
        except:
            pass  # Ignore errors in persona action
        
        return 0.0
    
    def _calculate_exploration_bonus(self, action: GameAction) -> float:
        """Calculate bonus for exploration."""
        # Simple exploration bonus based on action diversity
        if len(self.decision_history) < 10:
            return 0.1  # Encourage early exploration
        
        recent_actions = [d['action'] for d in self.decision_history[-10:]]
        action_diversity = len(set(recent_actions)) / len(recent_actions)
        
        if action_diversity > 0.5:
            return 0.5  # Bonus for diverse actions
        
        return 0.0
    
    def _calculate_current_consistency(self) -> float:
        """Calculate current decision consistency."""
        if len(self.decision_history) < 10:
            return 0.0
        
        # Simple consistency calculation
        recent_decisions = self.decision_history[-10:]
        
        # Group by similar states
        state_actions = {}
        for decision in recent_decisions:
            state_key = f"{decision['player_value']}_{decision['dealer_upcard']}"
            if state_key not in state_actions:
                state_actions[state_key] = []
            state_actions[state_key].append(decision['action'])
        
        # Calculate consistency
        total_situations = 0
        consistent_situations = 0
        
        for actions in state_actions.values():
            if len(actions) >= 2:
                total_situations += 1
                if len(set(actions)) == 1:
                    consistent_situations += 1
        
        if total_situations == 0:
            return 0.0
        
        return consistent_situations / total_situations
    
    def track_decision(self, action: GameAction) -> None:
        """Track decision for consistency analysis."""
        if self.current_state:
            decision_record = {
                'player_value': self.current_state.player_value,
                'dealer_upcard': self.current_state.dealer_upcard,
                'action': action.value,
                'episode_step': self.episode_step
            }
            
            self.decision_history.append(decision_record)
            
            # Keep only recent decisions
            if len(self.decision_history) > 100:
                self.decision_history.pop(0)
    
    def get_available_actions(self) -> List[GameAction]:
        """Get available actions in current state."""
        return self.game.get_available_actions()
    
    def get_episode_stats(self) -> Dict[str, Any]:
        """Get current episode statistics."""
        return self.episode_stats.copy()
    
    def set_persona(self, persona: Optional[BasePersona]) -> None:
        """Set or change the persona."""
        self.persona = persona
        if persona:
            self.hands_since_switch = 0
        else:
            self.hands_since_switch += 1
