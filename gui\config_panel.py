"""
Configuration Panel Widget for BlackJack Bot ML GUI.

This module provides settings and configuration management for
game rules, agent parameters, and GUI preferences.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, Callable
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ConfigPanelWidget:
    """
    Widget for configuration and settings management.
    
    Provides interface for adjusting game rules, agent settings,
    and GUI preferences.
    """
    
    def __init__(self, parent, on_config_change: Optional[Callable] = None):
        """
        Initialize the configuration panel widget.
        
        Args:
            parent: Parent tkinter widget
            on_config_change: Callback for configuration changes
        """
        self.parent = parent
        self.on_config_change = on_config_change
        
        # Configuration state
        self.config = self.get_default_config()
        
        self.create_widgets()
        
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration settings."""
        return {
            # Game rules
            'num_decks': 6,
            'dealer_hits_soft_17': True,
            'double_after_split': True,
            'blackjack_payout': 1.5,
            
            # Display settings
            'show_probabilities': False,
            'show_card_count': False,
            'auto_refresh_advice': True,
            'show_alternative_actions': True,
            
            # Agent settings
            'default_agent': 'Basic Strategy',
            'show_confidence': True,
            'show_reasoning': True,
            'enable_agent_comparison': True,
            
            # Advanced settings
            'simulation_hands': 1000,
            'enable_logging': False,
            'debug_mode': False
        }
        
    def create_widgets(self):
        """Create all configuration widgets."""
        # Main container with notebook for tabs
        self.notebook = ttk.Notebook(self.parent)
        
        # Create configuration tabs
        self.create_game_rules_tab()
        self.create_display_tab()
        self.create_agent_tab()
        self.create_advanced_tab()
        
        # Control buttons
        self.create_control_buttons()
        
    def create_game_rules_tab(self):
        """Create game rules configuration tab."""
        self.game_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.game_frame, text="Game Rules")
        
        # Number of decks
        self.decks_frame = ttk.Frame(self.game_frame)
        ttk.Label(self.decks_frame, text="Number of Decks:").grid(row=0, column=0, sticky='w')
        
        self.decks_var = tk.IntVar(value=self.config['num_decks'])
        self.decks_spinbox = ttk.Spinbox(
            self.decks_frame,
            from_=1, to=8,
            textvariable=self.decks_var,
            width=10,
            command=self.on_config_update
        )
        self.decks_spinbox.grid(row=0, column=1, padx=(10, 0))
        
        # Dealer hits soft 17
        self.soft17_var = tk.BooleanVar(value=self.config['dealer_hits_soft_17'])
        self.soft17_check = ttk.Checkbutton(
            self.game_frame,
            text="Dealer hits soft 17 (H17)",
            variable=self.soft17_var,
            command=self.on_config_update
        )
        
        # Double after split
        self.das_var = tk.BooleanVar(value=self.config['double_after_split'])
        self.das_check = ttk.Checkbutton(
            self.game_frame,
            text="Double after split allowed (DAS)",
            variable=self.das_var,
            command=self.on_config_update
        )
        
        # Blackjack payout
        self.payout_frame = ttk.Frame(self.game_frame)
        ttk.Label(self.payout_frame, text="Blackjack Payout:").grid(row=0, column=0, sticky='w')
        
        self.payout_var = tk.DoubleVar(value=self.config['blackjack_payout'])
        self.payout_spinbox = ttk.Spinbox(
            self.payout_frame,
            from_=1.0, to=2.0, increment=0.1,
            textvariable=self.payout_var,
            width=10,
            format="%.1f",
            command=self.on_config_update
        )
        self.payout_spinbox.grid(row=0, column=1, padx=(10, 0))
        
        ttk.Label(self.payout_frame, text="(1.5 = 3:2, 1.2 = 6:5)").grid(row=0, column=2, padx=(10, 0))
        
    def create_display_tab(self):
        """Create display settings tab."""
        self.display_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.display_frame, text="Display")
        
        # Show probabilities
        self.prob_var = tk.BooleanVar(value=self.config['show_probabilities'])
        self.prob_check = ttk.Checkbutton(
            self.display_frame,
            text="Show win probabilities",
            variable=self.prob_var,
            command=self.on_config_update
        )
        
        # Show card count
        self.count_var = tk.BooleanVar(value=self.config['show_card_count'])
        self.count_check = ttk.Checkbutton(
            self.display_frame,
            text="Show card count information",
            variable=self.count_var,
            command=self.on_config_update
        )
        
        # Auto refresh advice
        self.auto_refresh_var = tk.BooleanVar(value=self.config['auto_refresh_advice'])
        self.auto_refresh_check = ttk.Checkbutton(
            self.display_frame,
            text="Auto-refresh advice on card changes",
            variable=self.auto_refresh_var,
            command=self.on_config_update
        )
        
        # Show alternative actions
        self.alternatives_var = tk.BooleanVar(value=self.config['show_alternative_actions'])
        self.alternatives_check = ttk.Checkbutton(
            self.display_frame,
            text="Show alternative actions",
            variable=self.alternatives_var,
            command=self.on_config_update
        )
        
    def create_agent_tab(self):
        """Create agent settings tab."""
        self.agent_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.agent_frame, text="AI Agents")
        
        # Default agent
        self.default_agent_frame = ttk.Frame(self.agent_frame)
        ttk.Label(self.default_agent_frame, text="Default Agent:").grid(row=0, column=0, sticky='w')
        
        self.default_agent_var = tk.StringVar(value=self.config['default_agent'])
        self.default_agent_combo = ttk.Combobox(
            self.default_agent_frame,
            textvariable=self.default_agent_var,
            values=["Basic Strategy", "Cautious Persona", "Aggressive Persona", 
                   "Intuitive Persona", "Dynamic Persona"],
            state="readonly",
            width=20
        )
        self.default_agent_combo.grid(row=0, column=1, padx=(10, 0))
        self.default_agent_combo.bind('<<ComboboxSelected>>', self.on_config_update)
        
        # Show confidence
        self.confidence_var = tk.BooleanVar(value=self.config['show_confidence'])
        self.confidence_check = ttk.Checkbutton(
            self.agent_frame,
            text="Show confidence levels",
            variable=self.confidence_var,
            command=self.on_config_update
        )
        
        # Show reasoning
        self.reasoning_var = tk.BooleanVar(value=self.config['show_reasoning'])
        self.reasoning_check = ttk.Checkbutton(
            self.agent_frame,
            text="Show decision reasoning",
            variable=self.reasoning_var,
            command=self.on_config_update
        )
        
        # Enable agent comparison
        self.comparison_var = tk.BooleanVar(value=self.config['enable_agent_comparison'])
        self.comparison_check = ttk.Checkbutton(
            self.agent_frame,
            text="Enable agent comparison features",
            variable=self.comparison_var,
            command=self.on_config_update
        )
        
    def create_advanced_tab(self):
        """Create advanced settings tab."""
        self.advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.advanced_frame, text="Advanced")
        
        # Simulation hands
        self.sim_frame = ttk.Frame(self.advanced_frame)
        ttk.Label(self.sim_frame, text="Simulation Hands:").grid(row=0, column=0, sticky='w')
        
        self.sim_var = tk.IntVar(value=self.config['simulation_hands'])
        self.sim_spinbox = ttk.Spinbox(
            self.sim_frame,
            from_=100, to=10000, increment=100,
            textvariable=self.sim_var,
            width=10,
            command=self.on_config_update
        )
        self.sim_spinbox.grid(row=0, column=1, padx=(10, 0))
        
        # Enable logging
        self.logging_var = tk.BooleanVar(value=self.config['enable_logging'])
        self.logging_check = ttk.Checkbutton(
            self.advanced_frame,
            text="Enable detailed logging",
            variable=self.logging_var,
            command=self.on_config_update
        )
        
        # Debug mode
        self.debug_var = tk.BooleanVar(value=self.config['debug_mode'])
        self.debug_check = ttk.Checkbutton(
            self.advanced_frame,
            text="Enable debug mode",
            variable=self.debug_var,
            command=self.on_config_update
        )
        
        # Performance info
        self.perf_frame = ttk.LabelFrame(self.advanced_frame, text="Performance", padding="5")
        self.perf_info = ttk.Label(
            self.perf_frame,
            text="GUI performance: Excellent\nMemory usage: Low\nAgent response: Fast",
            font=('Arial', 9),
            foreground='green'
        )
        
    def create_control_buttons(self):
        """Create control buttons for configuration management."""
        self.control_frame = ttk.Frame(self.parent)
        
        # Reset to defaults button
        self.reset_btn = ttk.Button(
            self.control_frame,
            text="Reset Defaults",
            command=self.reset_to_defaults,
            width=12
        )
        
        # Save configuration button
        self.save_btn = ttk.Button(
            self.control_frame,
            text="Save Config",
            command=self.save_configuration,
            width=12
        )
        
        # Load configuration button
        self.load_btn = ttk.Button(
            self.control_frame,
            text="Load Config",
            command=self.load_configuration,
            width=12
        )
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Configure parent grid
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)
        
        # Notebook
        self.notebook.grid(row=0, column=0, sticky='nsew', pady=(0, 10))
        
        # Layout game rules tab
        self.game_frame.grid_columnconfigure(0, weight=1)
        
        self.decks_frame.grid(row=0, column=0, sticky='ew', pady=5)
        self.soft17_check.grid(row=1, column=0, sticky='w', pady=5)
        self.das_check.grid(row=2, column=0, sticky='w', pady=5)
        self.payout_frame.grid(row=3, column=0, sticky='ew', pady=5)
        
        # Layout display tab
        self.display_frame.grid_columnconfigure(0, weight=1)
        
        self.prob_check.grid(row=0, column=0, sticky='w', pady=5)
        self.count_check.grid(row=1, column=0, sticky='w', pady=5)
        self.auto_refresh_check.grid(row=2, column=0, sticky='w', pady=5)
        self.alternatives_check.grid(row=3, column=0, sticky='w', pady=5)
        
        # Layout agent tab
        self.agent_frame.grid_columnconfigure(0, weight=1)
        
        self.default_agent_frame.grid(row=0, column=0, sticky='ew', pady=5)
        self.confidence_check.grid(row=1, column=0, sticky='w', pady=5)
        self.reasoning_check.grid(row=2, column=0, sticky='w', pady=5)
        self.comparison_check.grid(row=3, column=0, sticky='w', pady=5)
        
        # Layout advanced tab
        self.advanced_frame.grid_columnconfigure(0, weight=1)
        
        self.sim_frame.grid(row=0, column=0, sticky='ew', pady=5)
        self.logging_check.grid(row=1, column=0, sticky='w', pady=5)
        self.debug_check.grid(row=2, column=0, sticky='w', pady=5)
        self.perf_frame.grid(row=3, column=0, sticky='ew', pady=10)
        self.perf_info.grid(row=0, column=0, sticky='w')
        
        # Control buttons
        self.control_frame.grid(row=1, column=0, sticky='ew')
        self.control_frame.grid_columnconfigure(0, weight=1)
        self.control_frame.grid_columnconfigure(1, weight=1)
        self.control_frame.grid_columnconfigure(2, weight=1)
        
        self.reset_btn.grid(row=0, column=0, padx=(0, 5), pady=5)
        self.save_btn.grid(row=0, column=1, padx=5, pady=5)
        self.load_btn.grid(row=0, column=2, padx=(5, 0), pady=5)
        
    def on_config_update(self, event=None):
        """Handle configuration updates."""
        # Update configuration dictionary
        self.config.update({
            'num_decks': self.decks_var.get(),
            'dealer_hits_soft_17': self.soft17_var.get(),
            'double_after_split': self.das_var.get(),
            'blackjack_payout': self.payout_var.get(),
            'show_probabilities': self.prob_var.get(),
            'show_card_count': self.count_var.get(),
            'auto_refresh_advice': self.auto_refresh_var.get(),
            'show_alternative_actions': self.alternatives_var.get(),
            'default_agent': self.default_agent_var.get(),
            'show_confidence': self.confidence_var.get(),
            'show_reasoning': self.reasoning_var.get(),
            'enable_agent_comparison': self.comparison_var.get(),
            'simulation_hands': self.sim_var.get(),
            'enable_logging': self.logging_var.get(),
            'debug_mode': self.debug_var.get()
        })
        
        # Notify callback
        if self.on_config_change:
            self.on_config_change(self.config.copy())
            
    def reset_to_defaults(self):
        """Reset all settings to default values."""
        if messagebox.askyesno("Reset Configuration", 
                              "Reset all settings to default values?"):
            self.config = self.get_default_config()
            self.update_widgets_from_config()
            self.on_config_update()
            
    def update_widgets_from_config(self):
        """Update widget values from configuration."""
        self.decks_var.set(self.config['num_decks'])
        self.soft17_var.set(self.config['dealer_hits_soft_17'])
        self.das_var.set(self.config['double_after_split'])
        self.payout_var.set(self.config['blackjack_payout'])
        self.prob_var.set(self.config['show_probabilities'])
        self.count_var.set(self.config['show_card_count'])
        self.auto_refresh_var.set(self.config['auto_refresh_advice'])
        self.alternatives_var.set(self.config['show_alternative_actions'])
        self.default_agent_var.set(self.config['default_agent'])
        self.confidence_var.set(self.config['show_confidence'])
        self.reasoning_var.set(self.config['show_reasoning'])
        self.comparison_var.set(self.config['enable_agent_comparison'])
        self.sim_var.set(self.config['simulation_hands'])
        self.logging_var.set(self.config['enable_logging'])
        self.debug_var.set(self.config['debug_mode'])
        
    def save_configuration(self):
        """Save current configuration to file."""
        try:
            # In a real implementation, this would save to a file
            messagebox.showinfo("Save Configuration", 
                              "Configuration saved successfully!")
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save configuration: {str(e)}")
            
    def load_configuration(self):
        """Load configuration from file."""
        try:
            # In a real implementation, this would load from a file
            messagebox.showinfo("Load Configuration", 
                              "Configuration loaded successfully!")
        except Exception as e:
            messagebox.showerror("Load Error", f"Failed to load configuration: {str(e)}")
            
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration."""
        return self.config.copy()
        
    def set_config(self, new_config: Dict[str, Any]):
        """Set configuration from external source."""
        self.config.update(new_config)
        self.update_widgets_from_config()
        self.on_config_update()
