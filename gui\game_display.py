"""
Game Display Widget for BlackJack Bot ML GUI.

This module implements visual card representation and game state display
with proper suit symbols and card formatting.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Optional
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.card import Card, Suit, Rank
from core.hand import Hand


class GameDisplayWidget:
    """
    Widget for displaying the current game state with visual card representations.
    
    Shows dealer and player cards with proper suit symbols and hand values.
    """
    
    def __init__(self, parent):
        """
        Initialize the game display widget.
        
        Args:
            parent: Parent tkinter widget
        """
        self.parent = parent
        
        # Current game state
        self.dealer_cards = []
        self.player_cards = []
        
        # Suit symbols for display
        self.suit_symbols = {
            Suit.HEARTS: "♥",
            Suit.DIAMONDS: "♦", 
            Suit.CLUBS: "♣",
            Suit.SPADES: "♠"
        }
        
        # Colors for suits
        self.suit_colors = {
            Suit.HEARTS: "red",
            Suit.DIAMONDS: "red",
            Suit.CLUBS: "black", 
            Suit.SPADES: "black"
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create all display widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        
        # Game title
        self.title_label = ttk.Label(
            self.main_frame,
            text="Current Hand",
            font=('Arial', 14, 'bold')
        )
        
        # Dealer section
        self.create_dealer_display()
        
        # Player section
        self.create_player_display()
        
        # Hand analysis section
        self.create_analysis_display()
        
    def create_dealer_display(self):
        """Create dealer cards display section."""
        self.dealer_frame = ttk.LabelFrame(
            self.main_frame,
            text="Dealer",
            padding="10"
        )
        
        # Dealer cards display area
        self.dealer_cards_frame = ttk.Frame(self.dealer_frame)
        
        # Dealer hand value
        self.dealer_value_label = ttk.Label(
            self.dealer_frame,
            text="Value: -",
            font=('Arial', 11, 'bold'),
            foreground='blue'
        )
        
        # Dealer status
        self.dealer_status_label = ttk.Label(
            self.dealer_frame,
            text="No cards selected",
            font=('Arial', 10),
            foreground='gray'
        )
        
    def create_player_display(self):
        """Create player cards display section."""
        self.player_frame = ttk.LabelFrame(
            self.main_frame,
            text="Player",
            padding="10"
        )
        
        # Player cards display area
        self.player_cards_frame = ttk.Frame(self.player_frame)
        
        # Player hand value
        self.player_value_label = ttk.Label(
            self.player_frame,
            text="Value: -",
            font=('Arial', 11, 'bold'),
            foreground='green'
        )
        
        # Player status
        self.player_status_label = ttk.Label(
            self.player_frame,
            text="No cards selected",
            font=('Arial', 10),
            foreground='gray'
        )
        
    def create_analysis_display(self):
        """Create hand analysis display section."""
        self.analysis_frame = ttk.LabelFrame(
            self.main_frame,
            text="Hand Analysis",
            padding="10"
        )
        
        # Analysis text widget
        self.analysis_text = tk.Text(
            self.analysis_frame,
            height=6,
            width=40,
            wrap=tk.WORD,
            font=('Arial', 10),
            state=tk.DISABLED,
            bg='#f8f8f8',
            relief=tk.FLAT,
            borderwidth=1
        )
        
        # Scrollbar for analysis text
        self.analysis_scrollbar = ttk.Scrollbar(
            self.analysis_frame,
            orient=tk.VERTICAL,
            command=self.analysis_text.yview
        )
        self.analysis_text.configure(yscrollcommand=self.analysis_scrollbar.set)
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure grid weights
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(2, weight=1)
        self.main_frame.grid_rowconfigure(3, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        self.title_label.grid(row=0, column=0, pady=(0, 15))
        
        # Dealer section
        self.dealer_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
        self.dealer_frame.grid_columnconfigure(0, weight=1)
        
        self.dealer_cards_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        self.dealer_value_label.grid(row=1, column=0, sticky='w')
        self.dealer_status_label.grid(row=2, column=0, sticky='w')
        
        # Player section
        self.player_frame.grid(row=2, column=0, sticky='ew', pady=(0, 10))
        self.player_frame.grid_columnconfigure(0, weight=1)
        
        self.player_cards_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        self.player_value_label.grid(row=1, column=0, sticky='w')
        self.player_status_label.grid(row=2, column=0, sticky='w')
        
        # Analysis section
        self.analysis_frame.grid(row=3, column=0, sticky='nsew')
        self.analysis_frame.grid_rowconfigure(0, weight=1)
        self.analysis_frame.grid_columnconfigure(0, weight=1)
        
        self.analysis_text.grid(row=0, column=0, sticky='nsew')
        self.analysis_scrollbar.grid(row=0, column=1, sticky='ns')
        
    def update_cards(self, dealer_cards: List[Card], player_cards: List[Card]):
        """
        Update the display with new cards.
        
        Args:
            dealer_cards: List of dealer cards
            player_cards: List of player cards
        """
        self.dealer_cards = dealer_cards.copy()
        self.player_cards = player_cards.copy()
        
        self.update_dealer_display()
        self.update_player_display()
        self.update_analysis()
        
    def update_dealer_display(self):
        """Update dealer cards display."""
        # Clear existing card displays
        for widget in self.dealer_cards_frame.winfo_children():
            widget.destroy()
            
        if not self.dealer_cards:
            self.dealer_value_label.config(text="Value: -")
            self.dealer_status_label.config(text="No cards selected")
            return
            
        # Create card displays
        for i, card in enumerate(self.dealer_cards):
            card_label = self.create_card_label(self.dealer_cards_frame, card)
            card_label.grid(row=0, column=i, padx=5)
            
        # Calculate and display hand value
        hand = Hand()
        for card in self.dealer_cards:
            hand.add_card(card)
            
        value = hand.get_value()
        self.dealer_value_label.config(text=f"Value: {value}")
        
        # Update status
        status = self.get_hand_status(hand)
        self.dealer_status_label.config(text=status)
        
    def update_player_display(self):
        """Update player cards display."""
        # Clear existing card displays
        for widget in self.player_cards_frame.winfo_children():
            widget.destroy()
            
        if not self.player_cards:
            self.player_value_label.config(text="Value: -")
            self.player_status_label.config(text="No cards selected")
            return
            
        # Create card displays
        for i, card in enumerate(self.player_cards):
            card_label = self.create_card_label(self.player_cards_frame, card)
            card_label.grid(row=0, column=i, padx=5)
            
        # Calculate and display hand value
        hand = Hand()
        for card in self.player_cards:
            hand.add_card(card)
            
        value = hand.get_value()
        self.player_value_label.config(text=f"Value: {value}")
        
        # Update status
        status = self.get_hand_status(hand)
        self.player_status_label.config(text=status)
        
    def create_card_label(self, parent, card: Card) -> ttk.Label:
        """
        Create a visual label for a card.
        
        Args:
            parent: Parent widget
            card: Card to display
            
        Returns:
            Label widget for the card
        """
        # Get card display text
        rank_text = self.get_rank_display(card.rank)
        suit_symbol = self.suit_symbols[card.suit]
        card_text = f"{rank_text}{suit_symbol}"
        
        # Get suit color
        color = self.suit_colors[card.suit]
        
        # Create label with card styling
        label = ttk.Label(
            parent,
            text=card_text,
            font=('Arial', 16, 'bold'),
            foreground=color,
            background='white',
            relief=tk.RAISED,
            borderwidth=2,
            padding=(8, 4)
        )
        
        return label
        
    def get_rank_display(self, rank: Rank) -> str:
        """Get display text for a rank."""
        if rank == Rank.ACE:
            return "A"
        elif rank == Rank.JACK:
            return "J"
        elif rank == Rank.QUEEN:
            return "Q"
        elif rank == Rank.KING:
            return "K"
        else:
            return str(rank.value)
            
    def get_hand_status(self, hand: Hand) -> str:
        """
        Get status description for a hand.
        
        Args:
            hand: Hand to analyze
            
        Returns:
            Status description string
        """
        if hand.is_blackjack():
            return "Blackjack! (Natural 21)"
        elif hand.is_bust():
            return "Bust (Over 21)"
        elif hand.is_soft():
            return f"Soft {hand.get_value()} (Contains Ace as 11)"
        elif hand.can_split():
            return f"Pair of {self.get_rank_display(hand.cards[0].rank)}s"
        else:
            return f"Hard {hand.get_value()}"
            
    def update_analysis(self):
        """Update the hand analysis display."""
        # Enable text widget for editing
        self.analysis_text.config(state=tk.NORMAL)
        self.analysis_text.delete(1.0, tk.END)
        
        if not self.dealer_cards or not self.player_cards:
            self.analysis_text.insert(tk.END, "Select dealer and player cards to see analysis.")
            self.analysis_text.config(state=tk.DISABLED)
            return
            
        # Create hands for analysis
        dealer_hand = Hand()
        for card in self.dealer_cards:
            dealer_hand.add_card(card)
            
        player_hand = Hand()
        for card in self.player_cards:
            player_hand.add_card(card)
            
        # Generate analysis text
        analysis = self.generate_hand_analysis(dealer_hand, player_hand)
        self.analysis_text.insert(tk.END, analysis)
        
        # Disable text widget
        self.analysis_text.config(state=tk.DISABLED)
        
    def generate_hand_analysis(self, dealer_hand: Hand, player_hand: Hand) -> str:
        """
        Generate detailed hand analysis text.
        
        Args:
            dealer_hand: Dealer's hand
            player_hand: Player's hand
            
        Returns:
            Analysis text
        """
        analysis_lines = []
        
        # Basic hand information
        analysis_lines.append("HAND ANALYSIS")
        analysis_lines.append("=" * 20)
        analysis_lines.append("")
        
        # Dealer analysis
        dealer_value = dealer_hand.get_value()
        dealer_upcard = dealer_hand.cards[0] if dealer_hand.cards else None
        
        analysis_lines.append(f"Dealer Upcard: {self.get_rank_display(dealer_upcard.rank)} ({dealer_upcard.get_value()})")
        analysis_lines.append(f"Dealer Strength: {self.assess_dealer_strength(dealer_upcard)}")
        analysis_lines.append("")
        
        # Player analysis
        player_value = player_hand.get_value()
        analysis_lines.append(f"Player Hand: {player_value}")
        analysis_lines.append(f"Hand Type: {self.get_hand_status(player_hand)}")
        
        # Strategic considerations
        analysis_lines.append("")
        analysis_lines.append("STRATEGIC NOTES:")
        
        if player_hand.is_blackjack():
            analysis_lines.append("• You have blackjack! Automatic win unless dealer also has blackjack.")
        elif player_hand.is_bust():
            analysis_lines.append("• Hand is bust. You lose automatically.")
        elif player_hand.can_split():
            analysis_lines.append("• You have a pair. Consider splitting based on dealer upcard.")
        elif player_hand.is_soft():
            analysis_lines.append("• Soft hand gives you flexibility. Ace can count as 1 or 11.")
        
        # Dealer considerations
        if dealer_upcard and dealer_upcard.get_value() <= 6:
            analysis_lines.append("• Dealer has weak upcard. Consider standing on lower totals.")
        elif dealer_upcard and dealer_upcard.get_value() >= 7:
            analysis_lines.append("• Dealer has strong upcard. You may need to take more risk.")
            
        if dealer_upcard and dealer_upcard.is_ace:
            analysis_lines.append("• Dealer showing Ace. High chance of strong hand.")
            
        return "\n".join(analysis_lines)
        
    def assess_dealer_strength(self, upcard: Card) -> str:
        """
        Assess dealer strength based on upcard.
        
        Args:
            upcard: Dealer's upcard
            
        Returns:
            Strength assessment string
        """
        value = upcard.get_value()
        
        if upcard.is_ace:
            return "Very Strong (Ace)"
        elif value >= 10:
            return "Strong (10-value)"
        elif value >= 7:
            return "Moderate (7-9)"
        elif value >= 4:
            return "Weak (4-6)"
        else:
            return "Very Weak (2-3)"
            
    def reset(self):
        """Reset the display to initial state."""
        self.dealer_cards = []
        self.player_cards = []
        
        # Clear card displays
        for widget in self.dealer_cards_frame.winfo_children():
            widget.destroy()
        for widget in self.player_cards_frame.winfo_children():
            widget.destroy()
            
        # Reset labels
        self.dealer_value_label.config(text="Value: -")
        self.dealer_status_label.config(text="No cards selected")
        self.player_value_label.config(text="Value: -")
        self.player_status_label.config(text="No cards selected")
        
        # Clear analysis
        self.analysis_text.config(state=tk.NORMAL)
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, "Select dealer and player cards to see analysis.")
        self.analysis_text.config(state=tk.DISABLED)
