#!/usr/bin/env python3
"""
Simple validation script for Phase 3 training pipeline.
Tests basic functionality without running full training.
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """Test all required imports."""
    print("🧪 Testing imports...")
    
    try:
        # Core RL imports
        from rl.training_pipeline import TrainingPipeline, TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        print("   ✅ RL components imported")
        
        # Persona imports
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        from personas.aggressive_persona import AggressivePersona
        from personas.basic_strategy_persona import BasicStrategyPersona
        print("   ✅ Persona components imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False


def test_configurations():
    """Test configuration creation."""
    print("\n📋 Testing configurations...")
    
    try:
        from rl.training_pipeline import TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        
        # Test DQN config
        dqn_config = DQNConfig(
            hidden_layers=[32, 16],
            learning_rate=0.01,
            batch_size=16,
            target_update_frequency=10,
            min_buffer_size=50,
            buffer_size=500,
            discount_factor=0.95
        )
        print("   ✅ DQN config created")
        
        # Test training config
        training_config = TrainingConfig(
            total_episodes=5,
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 2,
                TrainingPhase.LEARNING: 2,
                TrainingPhase.OPTIMIZATION: 1,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=3,
            evaluation_episodes=2,
            checkpoint_frequency=100,
            target_win_rate=0.4,
            target_consistency=0.8,
            max_detection_risk=0.3,
            early_stopping_enabled=False,
            patience=100,
            log_frequency=1
        )
        print("   ✅ Training config created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration failed: {e}")
        return False


def test_personas():
    """Test persona creation and switcher."""
    print("\n🎭 Testing personas...")
    
    try:
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        from personas.aggressive_persona import AggressivePersona
        from personas.basic_strategy_persona import BasicStrategyPersona
        
        # Create personas
        personas = {
            "cautious": CautiousPersona(),
            "aggressive": AggressivePersona(),
            "basic_strategy": BasicStrategyPersona()
        }
        print("   ✅ All personas created")
        
        # Test persona switcher
        switch_config = SwitchConfig(
            min_hands_per_persona=5,
            max_hands_per_persona=15,
            consistency_threshold=0.85
        )
        
        persona_switcher = PersonaSwitcher(switch_config)
        for name, persona in personas.items():
            persona_switcher.add_persona(name, persona)
        
        print(f"   ✅ Persona switcher created with {len(personas)} personas")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Persona test failed: {e}")
        return False


def test_environment():
    """Test RL environment creation."""
    print("\n🌍 Testing RL environment...")
    
    try:
        from rl.rl_environment import BlackjackRLEnvironment
        
        # Create environment
        env = BlackjackRLEnvironment(num_decks=6)
        print("   ✅ Environment created")
        
        # Test reset
        initial_state = env.reset()
        print("   ✅ Environment reset successful")
        
        # Test done property
        done_status = env.done
        print(f"   ✅ Done property accessible: {done_status}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Environment test failed: {e}")
        return False


def test_training_pipeline():
    """Test training pipeline creation (without running training)."""
    print("\n🏗️ Testing training pipeline...")
    
    try:
        from rl.training_pipeline import TrainingPipeline, TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        
        # Minimal configs
        dqn_config = DQNConfig(
            hidden_layers=[16],
            learning_rate=0.01,
            batch_size=8,
            target_update_frequency=5,
            min_buffer_size=20,
            buffer_size=100,
            discount_factor=0.95
        )
        
        evasion_config = EvasionConfig(
            consistency_threshold=0.9,
            pattern_detection_window=20,
            noise_intensity=0.1,
            technique_weights={
                EvasionTechnique.PERSONA_SWITCHING: 0.5,
                EvasionTechnique.BEHAVIORAL_NOISE: 0.5
            },
            target_consistency_range=(0.7, 0.85)
        )
        
        adaptation_config = AdaptationConfig(
            performance_window=10,
            performance_threshold=-0.1,
            min_adaptation_interval=5,
            lr_adaptation_enabled=True,
            exploration_adaptation_enabled=True,
            persona_adaptation_enabled=True,
            evasion_adaptation_enabled=True,
            consistency_adaptation_enabled=True,
            detection_risk_threshold=0.8
        )
        
        training_config = TrainingConfig(
            total_episodes=2,
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 1,
                TrainingPhase.LEARNING: 1,
                TrainingPhase.OPTIMIZATION: 0,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=10,
            evaluation_episodes=1,
            checkpoint_frequency=100,
            target_win_rate=0.4,
            target_consistency=0.8,
            max_detection_risk=0.3,
            early_stopping_enabled=False,
            patience=100,
            log_frequency=1
        )
        
        # Create persona switcher
        switch_config = SwitchConfig(
            min_hands_per_persona=2,
            max_hands_per_persona=5,
            consistency_threshold=0.85
        )
        
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona("cautious", CautiousPersona())
        
        # Create pipeline
        pipeline = TrainingPipeline(
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            training_config=training_config,
            persona_switcher=persona_switcher
        )
        print("   ✅ Training pipeline created")
        
        # Test initialization
        pipeline.initialize_training()
        print("   ✅ Training pipeline initialized")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all validation tests."""
    print("🎯 Phase 3 Training Pipeline Validation")
    print("=" * 50)
    
    start_time = time.time()
    
    tests = [
        ("Imports", test_imports),
        ("Configurations", test_configurations),
        ("Personas", test_personas),
        ("Environment", test_environment),
        ("Training Pipeline", test_training_pipeline)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("📊 VALIDATION RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    print(f"Validation Time: {duration:.2f} seconds")
    
    if passed == total:
        print("\n✅ ALL TESTS PASSED - Phase 3 pipeline is ready!")
        print("🚀 You can now run full training with:")
        print("   python scripts/train_phase3_agent.py --test")
        return 0
    else:
        print(f"\n❌ {total-passed} tests failed - Please fix issues before training")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
