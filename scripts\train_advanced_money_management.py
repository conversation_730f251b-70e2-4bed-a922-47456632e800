"""
Advanced Money Management Training Script for BlackJack Bot ML.

This script demonstrates the enhanced ML training pipeline with sophisticated
money management strategies including streak detection, loss recovery, and
adaptive betting patterns.
"""

import argparse
import sys
import time
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rl.training_pipeline import TrainingPipeline, TrainingConfig
from rl.dqn_agent import DQNConfig
from rl.evasion_strategies import EvasionConfig
from rl.adaptive_learning import AdaptationConfig
from rl.bankroll_manager import BankrollConfig, BettingStrategy
from rl.advanced_money_management import AdvancedMoneyConfig, BettingProgression
from personas.persona_switcher import <PERSON><PERSON><PERSON><PERSON><PERSON>, SwitchConfig
from core.game_rules import BlackjackRules, get_realistic_rules


def create_advanced_money_configs(test_mode=False, progression_strategy="modified_martingale"):
    """Create advanced money management configurations."""
    
    # Adjust parameters for test mode
    if test_mode:
        total_episodes = 10
        evaluation_episodes = 3
        evaluation_frequency = 5
        checkpoint_frequency = 20
        log_frequency = 1
        initial_bankroll = 100.0
        base_bet = 5.0
    else:
        # Production-grade training configuration
        total_episodes = 3000  # Production-grade episode count
        evaluation_episodes = 100
        evaluation_frequency = 500  # Checkpoint every 500 episodes
        checkpoint_frequency = 500
        log_frequency = 50  # Log every 50 episodes for monitoring
        initial_bankroll = 1000.0
        base_bet = 10.0

    # DQN Configuration (optimized for money management)
    dqn_config = DQNConfig(
        learning_rate=0.0005,  # Slightly lower for stable money management learning
        discount_factor=0.99,
        epsilon_start=0.9,
        epsilon_end=0.05,
        epsilon_decay=0.995,
        buffer_size=20000,  # Fixed: use buffer_size instead of memory_size
        batch_size=64,
        target_update_frequency=100,
        hidden_layers=[256, 256, 128],  # Larger network for complex money management
        activation='relu'
    )

    # Evasion Configuration
    from rl.evasion_strategies import EvasionTechnique
    evasion_config = EvasionConfig(
        consistency_threshold=0.92,
        pattern_detection_window=100,
        noise_intensity=0.15,
        technique_weights={
            EvasionTechnique.PERSONA_SWITCHING: 0.3,
            EvasionTechnique.BEHAVIORAL_NOISE: 0.25,
            EvasionTechnique.TIMING_VARIATION: 0.2,
            EvasionTechnique.DECISION_MASKING: 0.15,
            EvasionTechnique.PATTERN_DISRUPTION: 0.05,
            EvasionTechnique.ADAPTIVE_CONSISTENCY: 0.05
        },
        target_consistency_range=(0.75, 0.88),
        detection_penalty=-0.5
    )

    # Adaptation Configuration
    adaptation_config = AdaptationConfig(
        performance_window=50,
        min_adaptation_interval=25,
        detection_risk_threshold=0.6,
        performance_threshold=-0.05,  # Fixed: should be negative for decline detection
        lr_adaptation_enabled=True,
        exploration_adaptation_enabled=True,
        persona_adaptation_enabled=True,
        evasion_adaptation_enabled=True,
        consistency_adaptation_enabled=True
    )

    # Training Configuration (enhanced for money management)
    training_config = TrainingConfig(
        total_episodes=total_episodes,
        evaluation_frequency=evaluation_frequency,
        evaluation_episodes=evaluation_episodes,
        checkpoint_frequency=checkpoint_frequency,
        log_frequency=log_frequency,
        target_win_rate=0.55,  # Target 55% win rate for production deployment
        target_consistency=0.8,
        max_detection_risk=0.3,
        
        # Enhanced bankroll management
        bankroll_enabled=True,
        target_bankroll_growth=0.25,  # 25% growth target
        bankroll_optimization_weight=0.6,  # 60% bankroll, 40% win rate
        
        # Advanced money management
        advanced_money_management=True,
        stress_test_enabled=True,
        loss_streak_training=True,
        money_management_weight=0.8,  # 80% money management, 20% basic strategy
        
        # Early stopping
        early_stopping_enabled=True,
        patience=100  # More patience for money management learning
    )

    # Bankroll Configuration
    bankroll_config = BankrollConfig(
        initial_bankroll=initial_bankroll,
        base_bet_size=base_bet,
        min_bet_size=max(1.0, base_bet * 0.5),
        max_bet_size=min(100.0, initial_bankroll * 0.1),
        betting_strategy=BettingStrategy.KELLY,
        kelly_fraction=0.25,
        max_bet_percentage=0.08,  # 8% max bet
        confidence_threshold=0.6,
        confidence_multiplier=1.3
    )

    # Advanced Money Management Configuration
    progression_map = {
        "flat": BettingProgression.FLAT,
        "martingale": BettingProgression.MARTINGALE,
        "modified_martingale": BettingProgression.MODIFIED_MARTINGALE,
        "fibonacci": BettingProgression.FIBONACCI,
        "paroli": BettingProgression.PAROLI,
        "labouchere": BettingProgression.LABOUCHERE,
        "kelly_adaptive": BettingProgression.KELLY_ADAPTIVE
    }
    
    progression_strategy_enum = progression_map.get(progression_strategy.lower(), BettingProgression.MODIFIED_MARTINGALE)
    
    advanced_money_config = AdvancedMoneyConfig(
        initial_bankroll=initial_bankroll,
        base_bet_size=base_bet,
        min_bet_size=max(1.0, base_bet * 0.5),
        max_bet_size=min(100.0, initial_bankroll * 0.1),
        
        # Streak detection
        min_streak_length=3,
        max_streak_tracking=15,
        
        # Loss recovery
        loss_recovery_enabled=True,
        max_recovery_attempts=8,
        recovery_multiplier=1.4,
        
        # Win streak capitalization
        win_streak_enabled=True,
        win_streak_multiplier=1.2,
        max_win_streak_bet=base_bet * 4,
        
        # Betting progression
        progression_strategy=progression_strategy_enum,
        progression_reset_threshold=5,
        
        # Risk management
        max_consecutive_losses=12,
        emergency_stop_loss=0.3,  # Stop at 30% loss (production requirement)
        profit_protection=0.6,  # Protect 60% of profits
        
        # Adaptive parameters
        confidence_threshold=0.65,
        volatility_adjustment=True,
        bankroll_percentage_betting=True
    )

    return dqn_config, evasion_config, adaptation_config, training_config, bankroll_config, advanced_money_config


def run_advanced_money_training(test_mode=False, progression_strategy="modified_martingale", 
                               persona_mode="adaptive", verbose=False):
    """Run advanced money management training."""
    
    print("=" * 80)
    print("BlackJack Bot ML - Advanced Money Management Training")
    print("=" * 80)
    print(f"🎯 Mode: {'Test' if test_mode else 'Full Training'}")
    print(f"💰 Progression Strategy: {progression_strategy.title()}")
    print(f"🎭 Persona Mode: {persona_mode.title()}")
    print(f"📊 Verbose Logging: {'Enabled' if verbose else 'Disabled'}")
    print("=" * 80)
    print()

    # Create configurations
    print("📋 Creating advanced money management configurations...")
    configs = create_advanced_money_configs(test_mode, progression_strategy)
    dqn_config, evasion_config, adaptation_config, training_config, bankroll_config, advanced_money_config = configs
    
    # Display configuration summary
    print(f"   Episodes: {training_config.total_episodes}")
    print(f"   Initial Bankroll: ${advanced_money_config.initial_bankroll:.2f}")
    print(f"   Base Bet: ${advanced_money_config.base_bet_size:.2f}")
    print(f"   Progression: {advanced_money_config.progression_strategy.value}")
    print(f"   Money Management Weight: {training_config.money_management_weight*100:.0f}%")
    print(f"   Stress Testing: {'Enabled' if training_config.stress_test_enabled else 'Disabled'}")
    
    # Create persona switcher if needed
    persona_switcher = None
    if persona_mode != "none":
        switch_config = SwitchConfig(
            min_hands_per_persona=20,
            max_hands_per_persona=80,
            consistency_threshold=0.85,
            switch_on_loss_streak=4,
            switch_on_win_streak=7,
            random_switch_probability=0.03
        )
        persona_switcher = PersonaSwitcher(switch_config)
        print(f"   Persona Switching: {persona_mode} mode")

    # Create game rules
    game_rules = get_realistic_rules()
    print(f"   Game Rules: {game_rules.num_decks} decks, {game_rules.dealer_rule.value.upper()}")

    # Create training pipeline
    print("\n🏗️  Initializing advanced money management training pipeline...")
    pipeline = TrainingPipeline(
        dqn_config=dqn_config,
        evasion_config=evasion_config,
        adaptation_config=adaptation_config,
        training_config=training_config,
        persona_switcher=persona_switcher,
        game_rules=game_rules,
        bankroll_config=bankroll_config,
        advanced_money_config=advanced_money_config
    )

    # Initialize training
    print("🚀 Initializing training components...")
    pipeline.initialize_training()

    # Start training
    print(f"\n🎯 Starting advanced money management training...")
    print(f"   Target: {training_config.target_bankroll_growth*100:.0f}% bankroll growth")
    print(f"   Focus: Money management optimization with {progression_strategy} progression")
    print()

    start_time = time.time()

    try:
        final_metrics = pipeline.train()
        
        training_time = time.time() - start_time
        
        # Display final results
        print("\n" + "=" * 80)
        print("🎉 ADVANCED MONEY MANAGEMENT TRAINING COMPLETED")
        print("=" * 80)
        
        print(f"⏱️  Training Time: {training_time:.1f} seconds")
        print(f"📊 Total Episodes: {final_metrics.current_episode}")
        print(f"🏆 Best Episode: {final_metrics.best_episode}")
        print(f"📈 Final Win Rate: {final_metrics.win_rate_history[-1]:.3f}" if final_metrics.win_rate_history else "N/A")
        
        # Advanced money management results
        if hasattr(pipeline, 'advanced_money_manager') and pipeline.advanced_money_manager:
            money_report = pipeline.advanced_money_manager.get_comprehensive_report()
            
            print(f"\n💰 MONEY MANAGEMENT PERFORMANCE:")
            print(f"   Final Bankroll: ${money_report['current_state']['bankroll']:.2f}")
            print(f"   ROI: {money_report['current_state']['roi']*100:+.1f}%")
            print(f"   Net Profit: ${money_report['current_state']['bankroll'] - advanced_money_config.initial_bankroll:+.2f}")
            print(f"   Total Hands: {money_report['current_state']['total_hands']}")
            
            print(f"\n📊 STREAK ANALYSIS:")
            print(f"   Max Win Streak: {money_report['streak_analysis']['max_win_streak']}")
            print(f"   Max Loss Streak: {money_report['streak_analysis']['max_loss_streak']}")
            print(f"   Avg Win Streak: {money_report['streak_analysis']['avg_win_streak']:.1f}")
            print(f"   Avg Loss Streak: {money_report['streak_analysis']['avg_loss_streak']:.1f}")
            
            print(f"\n⚠️  RISK METRICS:")
            print(f"   Max Drawdown: {money_report['risk_metrics']['max_drawdown']*100:.1f}%")
            print(f"   Current Drawdown: {money_report['risk_metrics']['current_drawdown']*100:.1f}%")
            print(f"   Volatility: {money_report['risk_metrics']['volatility']:.3f}")
            print(f"   Sharpe Ratio: {money_report['risk_metrics']['sharpe_ratio']:.3f}")
            
            print(f"\n🎯 PROGRESSION PERFORMANCE:")
            print(f"   Strategy: {money_report['progression_performance']['strategy'].title()}")
            print(f"   Profit Factor: {money_report['progression_performance']['profit_factor']:.2f}")
        
        print("\n" + "=" * 80)
        
        return final_metrics
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
        return None
    except Exception as e:
        print(f"\n❌ Training failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Advanced Money Management Training for BlackJack Bot ML")
    
    parser.add_argument('--test', action='store_true', 
                       help='Run in test mode with minimal episodes')
    parser.add_argument('--progression', type=str, default='modified_martingale',
                       choices=['flat', 'martingale', 'modified_martingale', 'fibonacci', 'paroli', 'labouchere', 'kelly_adaptive'],
                       help='Betting progression strategy')
    parser.add_argument('--persona', type=str, default='adaptive',
                       choices=['none', 'discrete', 'hybrid', 'adaptive'],
                       help='Persona switching mode')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose episode-by-episode logging')
    
    args = parser.parse_args()
    
    # Run training
    final_metrics = run_advanced_money_training(
        test_mode=args.test,
        progression_strategy=args.progression,
        persona_mode=args.persona,
        verbose=args.verbose
    )
    
    if final_metrics:
        print("✅ Advanced money management training completed successfully!")
    else:
        print("❌ Advanced money management training failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
