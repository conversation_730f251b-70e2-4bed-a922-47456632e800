#!/usr/bin/env python3
"""
Quick test script for Phase 3 training pipeline.

This script runs a minimal training session to verify that all components
are working correctly without taking too long.
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from rl.training_pipeline import TrainingPipeline, TrainingConfig, TrainingPhase
from rl.dqn_agent import DQNConfig
from rl.evasion_strategies import EvasionConfig, EvasionTechnique
from rl.adaptive_learning import AdaptationConfig
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from personas.cautious_persona import CautiousPersona
from personas.aggressive_persona import AggressivePersona
from personas.basic_strategy_persona import BasicStrategyPersona


def create_test_configs():
    """Create minimal configurations for testing."""
    
    # Minimal DQN Configuration
    dqn_config = DQNConfig(
        hidden_layers=[32, 16],  # Smaller network
        learning_rate=0.01,      # Faster learning
        batch_size=16,           # Smaller batches
        target_update_frequency=10,
        min_buffer_size=50,
        buffer_size=500,
        discount_factor=0.95
    )
    
    # Minimal Evasion Configuration
    evasion_config = EvasionConfig(
        consistency_threshold=0.9,
        pattern_detection_window=20,
        noise_intensity=0.1,
        technique_weights={
            EvasionTechnique.PERSONA_SWITCHING: 0.5,
            EvasionTechnique.BEHAVIORAL_NOISE: 0.3,
            EvasionTechnique.TIMING_VARIATION: 0.2
        },
        target_consistency_range=(0.7, 0.85)
    )
    
    # Minimal Adaptive Learning Configuration
    adaptation_config = AdaptationConfig(
        performance_window=20,
        performance_threshold=-0.1,
        min_adaptation_interval=10,
        lr_adaptation_enabled=True,
        exploration_adaptation_enabled=True,
        persona_adaptation_enabled=True,
        evasion_adaptation_enabled=True,
        consistency_adaptation_enabled=True,
        detection_risk_threshold=0.8
    )
    
    # Minimal Training Configuration
    training_config = TrainingConfig(
        total_episodes=20,  # Very short training
        episodes_per_phase={
            TrainingPhase.EXPLORATION: 5,
            TrainingPhase.LEARNING: 10,
            TrainingPhase.OPTIMIZATION: 3,
            TrainingPhase.EVALUATION: 2
        },
        evaluation_frequency=10,  # Evaluate twice
        evaluation_episodes=3,    # Only 3 evaluation episodes
        checkpoint_frequency=100, # No checkpoints in this test
        target_win_rate=0.4,
        target_consistency=0.8,
        max_detection_risk=0.3,
        early_stopping_enabled=False,  # Disable early stopping
        patience=100,
        log_frequency=5
    )
    
    return dqn_config, evasion_config, adaptation_config, training_config


def create_test_persona_switcher():
    """Create minimal persona switcher for testing."""
    
    # Create personas
    personas = {
        "cautious": CautiousPersona(),
        "aggressive": AggressivePersona(),
        "basic_strategy": BasicStrategyPersona()
    }
    
    # Minimal switch configuration
    switch_config = SwitchConfig(
        min_hands_per_persona=5,
        max_hands_per_persona=15,
        consistency_threshold=0.85
    )
    
    # Create persona switcher
    persona_switcher = PersonaSwitcher(switch_config)
    for name, persona in personas.items():
        persona_switcher.add_persona(name, persona)
    
    return persona_switcher


def main():
    """Main test function."""
    print("🧪 Phase 3 Training Pipeline Test")
    print("=" * 40)
    
    start_time = time.time()
    
    try:
        # Create configurations
        print("📋 Creating test configurations...")
        dqn_config, evasion_config, adaptation_config, training_config = create_test_configs()
        
        # Create persona switcher
        print("🎭 Setting up persona switcher...")
        persona_switcher = create_test_persona_switcher()
        
        # Create training pipeline
        print("🏗️  Initializing training pipeline...")
        pipeline = TrainingPipeline(
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            training_config=training_config,
            persona_switcher=persona_switcher
        )
        
        # Initialize training
        print("🚀 Initializing training components...")
        pipeline.initialize_training()
        
        print(f"\n📊 Test Configuration:")
        print(f"   Total Episodes: {training_config.total_episodes}")
        print(f"   Evaluation Episodes: {training_config.evaluation_episodes}")
        print(f"   Network Size: {dqn_config.hidden_layers}")
        print(f"   Available Personas: {list(persona_switcher.personas.keys())}")
        
        # Start training
        print(f"\n🎯 Starting test training...")
        print("-" * 40)
        
        # Execute training
        final_metrics = pipeline.train()
        
        # Calculate test duration
        test_duration = time.time() - start_time
        
        # Display results
        print("\n" + "=" * 40)
        print("✅ TEST COMPLETED SUCCESSFULLY")
        print("=" * 40)
        
        training_summary = pipeline.get_training_summary()
        performance = training_summary["performance_summary"]
        
        print(f"📊 Test Results:")
        print(f"   Episodes Completed: {pipeline.metrics.current_episode}")
        print(f"   Final Win Rate: {performance['final_win_rate']:.3f}")
        print(f"   Test Duration: {test_duration:.2f} seconds")
        print(f"   Episodes/Second: {performance['episodes_per_second']:.2f}")
        
        # Get final agent statistics
        if pipeline.agent:
            final_stats = pipeline.agent.get_comprehensive_stats()
            
            print(f"\n🛡️  Evasion Performance:")
            evasion_metrics = final_stats["evasion_metrics"]
            print(f"   Total Evasions: {evasion_metrics['total_evasions']}")
            print(f"   Final Detection Risk: {final_stats['detection_assessment']['current_risk']:.3f}")
            
            print(f"\n🔄 Adaptive Learning:")
            adaptive_metrics = final_stats["adaptive_learning_metrics"]
            print(f"   Total Adaptations: {adaptive_metrics['total_adaptations']}")
            
            print(f"\n🎭 Persona Switching:")
            persona_stats = final_stats.get("persona_switcher_stats")
            if persona_stats:
                print(f"   Final Persona: {persona_stats['current_persona']}")
                print(f"   Total Switches: {persona_stats['switch_history']}")
            else:
                print("   No persona switching occurred")
        
        print(f"\n✅ All Phase 3 components working correctly!")
        print(f"   Training pipeline: ✅")
        print(f"   RL Agent: ✅")
        print(f"   Evasion strategies: ✅")
        print(f"   Adaptive learning: ✅")
        print(f"   Persona switching: ✅")
        print(f"   BasicStrategyPersona: ✅")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
