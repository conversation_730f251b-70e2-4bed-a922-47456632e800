"""
Demo script to test the complete GUI rule implementation.

This script demonstrates the new interactive blackjack GUI with complete
rule support including insurance, surrender, splitting, and side bets.
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.game_actions import GameActionsWidget
from gui.multi_hand_display import MultiHandDisplayWidget
from core.card import Card, Rank, Suit
from core.hand import Hand
from core.game_logic import GameState, GameAction


class GUIDemo:
    """Demo application for the complete GUI rule implementation."""
    
    def __init__(self):
        """Initialize the demo application."""
        self.root = tk.Tk()
        self.root.title("BlackJack Bot ML - Complete GUI Demo")
        self.root.geometry("1200x800")
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.create_widgets()
        self.setup_layout()
        
    def create_widgets(self):
        """Create all demo widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Title
        self.title_label = ttk.Label(
            self.main_frame,
            text="BlackJack Bot ML - Complete GUI Rule Implementation Demo",
            font=('Arial', 16, 'bold')
        )
        
        # Create notebook for different demo sections
        self.notebook = ttk.Notebook(self.main_frame)
        
        # Game Actions Demo Tab
        self.actions_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.actions_frame, text="Interactive Game Actions")
        
        self.game_actions = GameActionsWidget(
            self.actions_frame,
            on_action_taken=self.on_action_taken,
            on_game_state_change=self.on_game_state_change
        )
        
        # Multi-Hand Display Demo Tab
        self.display_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.display_frame, text="Multi-Hand Display")
        
        self.multi_display = MultiHandDisplayWidget(self.display_frame)
        
        # Demo Controls Tab
        self.controls_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.controls_frame, text="Demo Controls")
        
        self.create_demo_controls()
        
        # Status bar
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame,
            text="Demo ready - Try the interactive features!",
            relief='sunken',
            anchor='w'
        )
        
    def create_demo_controls(self):
        """Create demo control buttons."""
        # Demo scenarios section
        self.scenarios_frame = ttk.LabelFrame(
            self.controls_frame,
            text="Demo Scenarios",
            padding="10"
        )
        
        # Scenario buttons
        self.single_hand_btn = ttk.Button(
            self.scenarios_frame,
            text="Demo Single Hand",
            command=self.demo_single_hand
        )
        
        self.split_hand_btn = ttk.Button(
            self.scenarios_frame,
            text="Demo Split Hands",
            command=self.demo_split_hands
        )
        
        self.insurance_btn = ttk.Button(
            self.scenarios_frame,
            text="Demo Insurance",
            command=self.demo_insurance
        )
        
        self.side_bets_btn = ttk.Button(
            self.scenarios_frame,
            text="Demo Side Bets",
            command=self.demo_side_bets
        )
        
        # Reset button
        self.reset_btn = ttk.Button(
            self.scenarios_frame,
            text="Reset Demo",
            command=self.reset_demo
        )
        
        # Information section
        self.info_frame = ttk.LabelFrame(
            self.controls_frame,
            text="Feature Information",
            padding="10"
        )
        
        self.info_text = tk.Text(
            self.info_frame,
            height=15,
            width=50,
            wrap=tk.WORD,
            state='disabled'
        )
        
        self.info_scrollbar = ttk.Scrollbar(
            self.info_frame,
            orient='vertical',
            command=self.info_text.yview
        )
        self.info_text.configure(yscrollcommand=self.info_scrollbar.set)
        
        # Add feature information
        self.update_info_text("""
Complete GUI Rule Implementation Features:

✅ Insurance Betting Interface
- Automatic insurance option when dealer shows Ace
- Proper insurance bet sizing (half of main bet)
- 2:1 insurance payout calculation

✅ Surrender Options
- Early and late surrender support
- Proper surrender timing and payout (half bet return)

✅ Complete Splitting Interface
- Support for multiple splits (up to 3-4 hands)
- Split Aces restrictions handling
- Individual bet management per split hand

✅ Double Down Rule Enforcement
- Rule-based double down restrictions
- Proper bet sizing (exactly double original)
- Double down on split hands where allowed

✅ Side Bet Options
- Perfect Pairs side bet with payout calculation
- 21+3 side bet with combination detection
- Integration with ML advice system

✅ Enhanced Multi-Hand Display
- Clear visual separation between hands
- Current hand highlighting
- Comprehensive betting information display

✅ Production-Ready Integration
- Seamless AdviceEngine integration
- Bankroll management compatibility
- Comprehensive error handling and validation

Try the demo scenarios to see these features in action!
        """)
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure root grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # Configure main frame grid weights
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        self.title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Notebook
        self.notebook.grid(row=1, column=0, sticky='nsew')
        
        # Status bar
        self.status_frame.grid(row=2, column=0, sticky='ew', pady=(10, 0))
        self.status_label.grid(row=0, column=0, sticky='ew')
        self.status_frame.grid_columnconfigure(0, weight=1)
        
        # Layout child widgets
        self.game_actions.setup_layout()
        self.multi_display.setup_layout()
        
        # Demo controls layout
        self.scenarios_frame.grid(row=0, column=0, sticky='ew', padx=(0, 10))
        self.info_frame.grid(row=0, column=1, sticky='nsew')
        
        # Configure controls frame grid
        self.controls_frame.grid_rowconfigure(0, weight=1)
        self.controls_frame.grid_columnconfigure(1, weight=1)
        
        # Scenario buttons layout
        self.single_hand_btn.grid(row=0, column=0, pady=5, sticky='ew')
        self.split_hand_btn.grid(row=1, column=0, pady=5, sticky='ew')
        self.insurance_btn.grid(row=2, column=0, pady=5, sticky='ew')
        self.side_bets_btn.grid(row=3, column=0, pady=5, sticky='ew')
        self.reset_btn.grid(row=4, column=0, pady=(20, 5), sticky='ew')
        
        # Info text layout
        self.info_text.grid(row=0, column=0, sticky='nsew')
        self.info_scrollbar.grid(row=0, column=1, sticky='ns')
        self.info_frame.grid_rowconfigure(0, weight=1)
        self.info_frame.grid_columnconfigure(0, weight=1)
        
    def demo_single_hand(self):
        """Demonstrate single hand gameplay."""
        # Create demo game state
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.KING))
        player_hand.add_card(Card(Suit.SPADES, Rank.NINE))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        
        # Create mock game state
        game_state = self.create_mock_game_state([player_hand], dealer_hand)
        
        # Update displays
        bet_amounts = {0: 25.0}
        self.multi_display.update_game_state(game_state, bet_amounts)
        
        self.update_status("Demo: Single hand (K♥ 9♠) vs Dealer (7♣)")
        
    def demo_split_hands(self):
        """Demonstrate split hands gameplay."""
        # Create split hands
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.EIGHT))
        hand1.add_card(Card(Suit.CLUBS, Rank.THREE))
        
        hand2 = Hand()
        hand2.add_card(Card(Suit.SPADES, Rank.EIGHT))
        hand2.add_card(Card(Suit.DIAMONDS, Rank.KING))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.HEARTS, Rank.SIX))
        
        # Create mock game state
        game_state = self.create_mock_game_state([hand1, hand2], dealer_hand, current_hand=0)
        
        # Update displays
        bet_amounts = {0: 25.0, 1: 25.0}
        self.multi_display.update_game_state(game_state, bet_amounts)
        
        self.update_status("Demo: Split hands - Hand 1: (8♥ 3♣), Hand 2: (8♠ K♦) vs Dealer (6♥)")
        
    def demo_insurance(self):
        """Demonstrate insurance betting."""
        # Create hand with dealer Ace
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.KING))
        player_hand.add_card(Card(Suit.SPADES, Rank.QUEEN))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.ACE))
        
        # Create mock game state
        game_state = self.create_mock_game_state([player_hand], dealer_hand)
        
        # Update displays with insurance
        bet_amounts = {0: 25.0}
        insurance_bet = 12.5
        self.multi_display.update_game_state(
            game_state, bet_amounts, insurance_bet=insurance_bet
        )
        
        self.update_status("Demo: Insurance scenario - Dealer shows Ace, insurance bet placed")
        
    def demo_side_bets(self):
        """Demonstrate side bets."""
        # Create hand with perfect pair
        player_hand = Hand()
        player_hand.add_card(Card(Suit.HEARTS, Rank.JACK))
        player_hand.add_card(Card(Suit.SPADES, Rank.JACK))
        
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.CLUBS, Rank.QUEEN))
        
        # Create mock game state
        game_state = self.create_mock_game_state([player_hand], dealer_hand)
        
        # Update displays with side bets
        bet_amounts = {0: 25.0}
        side_bets = {'perfect_pairs': 5.0, '21+3': 5.0}
        self.multi_display.update_game_state(
            game_state, bet_amounts, side_bets=side_bets
        )
        
        self.update_status("Demo: Side bets - Perfect Pairs (J♥ J♠) and 21+3 bets placed")
        
    def reset_demo(self):
        """Reset the demo to initial state."""
        self.multi_display.clear_display()
        self.game_actions.reset_game()
        self.update_status("Demo reset - Ready for new demonstration")
        
    def create_mock_game_state(self, player_hands, dealer_hand, current_hand=0):
        """Create a mock game state for demonstration."""
        class MockGameState:
            def __init__(self, player_hands, dealer_hand, current_hand_index=0):
                self.player_hands = player_hands
                self.dealer_hand = dealer_hand
                self.current_hand_index = current_hand_index
                self.game_over = False
                self.results = []
                
        return MockGameState(player_hands, dealer_hand, current_hand)
        
    def on_action_taken(self, action, game_state):
        """Handle action taken in game actions widget."""
        self.update_status(f"Action taken: {action.value}")
        
    def on_game_state_change(self, game_state):
        """Handle game state change."""
        self.update_status("Game state updated")
        
    def update_status(self, message):
        """Update the status bar."""
        self.status_label.config(text=message)
        
    def update_info_text(self, text):
        """Update the information text."""
        self.info_text.config(state='normal')
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(tk.END, text.strip())
        self.info_text.config(state='disabled')
        
    def run(self):
        """Run the demo application."""
        try:
            self.update_status("GUI Demo started successfully - Try the interactive features!")
            self.root.mainloop()
        except Exception as e:
            print(f"Demo error: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    demo = GUIDemo()
    demo.run()
