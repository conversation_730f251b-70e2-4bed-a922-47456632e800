#!/usr/bin/env python3
"""
Performance test for optimized Phase 3 training pipeline.
Tests the evaluation phase specifically to verify optimizations.
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_evaluation_performance():
    """Test evaluation phase performance specifically."""
    print("🧪 Testing evaluation phase performance...")
    
    try:
        from rl.training_pipeline import <PERSON>Pipeline, TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        
        print("   ✅ Imports successful")
        
        # Create minimal configurations for fast testing
        dqn_config = DQNConfig(
            hidden_layers=[8],  # Very small network
            learning_rate=0.1,
            batch_size=2,
            target_update_frequency=1,
            min_buffer_size=2,
            buffer_size=10,
            discount_factor=0.9
        )
        
        evasion_config = EvasionConfig(
            consistency_threshold=0.9,
            pattern_detection_window=3,
            noise_intensity=0.1,
            technique_weights={
                EvasionTechnique.BEHAVIORAL_NOISE: 1.0
            },
            target_consistency_range=(0.7, 0.85)
        )
        
        adaptation_config = AdaptationConfig(
            performance_window=2,
            performance_threshold=-0.1,
            min_adaptation_interval=1,
            lr_adaptation_enabled=True,
            exploration_adaptation_enabled=True,
            persona_adaptation_enabled=False,
            evasion_adaptation_enabled=True,
            consistency_adaptation_enabled=True,
            detection_risk_threshold=0.8
        )
        
        # Test configuration with minimal evaluation episodes
        training_config = TrainingConfig(
            total_episodes=3,
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 1,
                TrainingPhase.LEARNING: 1,
                TrainingPhase.OPTIMIZATION: 1,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=2,  # Evaluate after episode 2
            evaluation_episodes=2,   # Only 2 evaluation episodes
            checkpoint_frequency=100,
            target_win_rate=0.4,
            target_consistency=0.8,
            max_detection_risk=0.3,
            early_stopping_enabled=False,
            patience=100,
            log_frequency=1
        )
        
        print("   ✅ Configurations created")
        
        # Create persona switcher
        switch_config = SwitchConfig(
            min_hands_per_persona=1,
            max_hands_per_persona=2,
            consistency_threshold=0.85
        )
        
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona("cautious", CautiousPersona())
        
        print("   ✅ Persona switcher created")
        
        # Create training pipeline
        pipeline = TrainingPipeline(
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            training_config=training_config,
            persona_switcher=persona_switcher
        )
        
        print("   ✅ Training pipeline created")
        
        # Initialize training
        pipeline.initialize_training()
        print("   ✅ Training initialized")
        
        # Test evaluation directly
        print("\n🎯 Testing evaluation phase performance...")
        
        # Run a few training episodes first
        for ep in range(2):
            episode_start = time.time()
            episode_metrics = pipeline._run_episode()
            episode_duration = time.time() - episode_start
            
            print(f"   Training Episode {ep}: {episode_duration:.2f}s "
                  f"(Reward: {episode_metrics['total_reward']:.2f}, "
                  f"Steps: {episode_metrics['steps']})")
            
            pipeline.metrics.current_episode = ep
            pipeline._update_metrics(episode_metrics)
        
        # Now test evaluation
        print("\n📊 Testing evaluation phase...")
        eval_start = time.time()
        
        evaluation_results = pipeline._evaluate_agent()
        
        eval_duration = time.time() - eval_start
        
        print(f"\n⏱️  Evaluation Performance:")
        print(f"   Total evaluation time: {eval_duration:.2f}s")
        print(f"   Episodes evaluated: {training_config.evaluation_episodes}")
        print(f"   Time per episode: {eval_duration/training_config.evaluation_episodes:.2f}s")
        print(f"   Evaluation win rate: {evaluation_results['win_rate']:.3f}")
        
        # Performance check
        if eval_duration < 10:  # Should complete in under 10 seconds
            print("   ✅ Evaluation performance: GOOD")
            return True
        else:
            print("   ⚠️  Evaluation performance: SLOW")
            return False
        
    except Exception as e:
        print(f"   ❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_pipeline_performance():
    """Test full pipeline with 3 episodes."""
    print("\n🚀 Testing full pipeline performance...")
    
    try:
        from scripts.train_phase3_agent import create_training_configs, create_persona_switcher
        from rl.training_pipeline import TrainingPipeline
        
        # Create test configurations
        dqn_config, evasion_config, adaptation_config, training_config = create_training_configs(test_mode=True)
        
        # Override to make it even faster
        training_config.total_episodes = 3
        training_config.evaluation_episodes = 2
        training_config.evaluation_frequency = 2
        
        persona_switcher = create_persona_switcher()
        
        print("   ✅ Test configurations created")
        
        # Create pipeline
        pipeline = TrainingPipeline(
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            training_config=training_config,
            persona_switcher=persona_switcher
        )
        
        # Initialize
        pipeline.initialize_training()
        print("   ✅ Pipeline initialized")
        
        # Run training with timing
        start_time = time.time()
        
        _ = pipeline.train()
        
        total_duration = time.time() - start_time
        
        print(f"\n⏱️  Full Pipeline Performance:")
        print(f"   Total time: {total_duration:.2f}s")
        print(f"   Episodes completed: {pipeline.metrics.current_episode}")
        print(f"   Time per episode: {total_duration/max(1, pipeline.metrics.current_episode):.2f}s")
        
        # Performance check
        if total_duration < 60:  # Should complete in under 1 minute
            print("   ✅ Full pipeline performance: GOOD")
            return True
        else:
            print("   ⚠️  Full pipeline performance: SLOW")
            return False
        
    except Exception as e:
        print(f"   ❌ Full pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run performance tests."""
    print("🎯 Phase 3 Training Pipeline Performance Test")
    print("=" * 50)
    
    start_time = time.time()
    
    tests = [
        ("Evaluation Phase", test_evaluation_performance),
        ("Full Pipeline", test_full_pipeline_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test: PASSED")
            else:
                print(f"❌ {test_name} test: FAILED (performance)")
        except Exception as e:
            print(f"❌ {test_name} test: CRASHED ({e})")
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 50)
    print("📊 PERFORMANCE TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    print(f"Total Test Time: {duration:.2f} seconds")
    
    if passed == total:
        print("\n✅ ALL PERFORMANCE TESTS PASSED")
        print("🚀 Training pipeline is optimized and ready!")
        return 0
    else:
        print(f"\n❌ {total-passed} performance tests failed")
        print("🔧 Further optimization needed")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
