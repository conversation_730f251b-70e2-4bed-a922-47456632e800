#!/usr/bin/env python3
"""
Simple training test with 2 episodes to verify progression.
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_training_progression():
    """Test training progression with 2 episodes."""
    print("🧪 Testing training progression...")
    
    try:
        from rl.training_pipeline import <PERSON><PERSON>ipeline, TrainingConfig, TrainingPhase
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        from personas.persona_switcher import PersonaSwitcher, SwitchConfig
        from personas.cautious_persona import CautiousPersona
        
        print("   ✅ Imports successful")
        
        # Create minimal configurations
        dqn_config = DQNConfig(
            hidden_layers=[16],
            learning_rate=0.01,
            batch_size=4,
            target_update_frequency=2,
            min_buffer_size=5,
            buffer_size=20,
            discount_factor=0.95
        )
        
        evasion_config = EvasionConfig(
            consistency_threshold=0.9,
            pattern_detection_window=5,
            noise_intensity=0.1,
            technique_weights={
                EvasionTechnique.BEHAVIORAL_NOISE: 1.0
            },
            target_consistency_range=(0.7, 0.85)
        )
        
        adaptation_config = AdaptationConfig(
            performance_window=3,
            performance_threshold=-0.1,
            min_adaptation_interval=2,
            lr_adaptation_enabled=True,
            exploration_adaptation_enabled=True,
            persona_adaptation_enabled=True,
            evasion_adaptation_enabled=True,
            consistency_adaptation_enabled=True,
            detection_risk_threshold=0.8
        )
        
        training_config = TrainingConfig(
            total_episodes=2,  # Just 2 episodes
            episodes_per_phase={
                TrainingPhase.EXPLORATION: 1,
                TrainingPhase.LEARNING: 1,
                TrainingPhase.OPTIMIZATION: 0,
                TrainingPhase.EVALUATION: 0
            },
            evaluation_frequency=10,  # No evaluation
            evaluation_episodes=1,
            checkpoint_frequency=100,  # No checkpoints
            target_win_rate=0.4,
            target_consistency=0.8,
            max_detection_risk=0.3,
            early_stopping_enabled=False,
            patience=100,
            log_frequency=1
        )
        
        print("   ✅ Configurations created")
        
        # Create persona switcher
        switch_config = SwitchConfig(
            min_hands_per_persona=1,
            max_hands_per_persona=3,
            consistency_threshold=0.85
        )
        
        persona_switcher = PersonaSwitcher(switch_config)
        persona_switcher.add_persona("cautious", CautiousPersona())
        
        print("   ✅ Persona switcher created")
        
        # Create training pipeline
        pipeline = TrainingPipeline(
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            training_config=training_config,
            persona_switcher=persona_switcher
        )
        
        print("   ✅ Training pipeline created")
        
        # Add simple callback
        def episode_callback(pipeline, episode_metrics):
            episode = episode_metrics["episode"]
            reward = episode_metrics.get("total_reward", 0)
            steps = episode_metrics.get("steps", 0)
            print(f"      Episode {episode}: Reward={reward:.2f}, Steps={steps}")
        
        pipeline.add_episode_callback(episode_callback)
        
        # Initialize training
        pipeline.initialize_training()
        print("   ✅ Training initialized")
        
        # Run training
        print("\n🎯 Running 2-episode training...")
        start_time = time.time()
        
        _ = pipeline.train()
        
        duration = time.time() - start_time
        
        print(f"\n📊 Training Results:")
        print(f"   Episodes Completed: {pipeline.metrics.current_episode}")
        print(f"   Training Time: {duration:.2f} seconds")
        
        # Get final stats
        if pipeline.agent:
            final_stats = pipeline.agent.get_comprehensive_stats()
            print(f"   Final Detection Risk: {final_stats['detection_assessment']['current_risk']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run simple training test."""
    print("🎯 Simple Training Progression Test")
    print("=" * 45)
    
    start_time = time.time()
    
    success = test_training_progression()
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 45)
    if success:
        print("✅ TRAINING PROGRESSION TEST PASSED")
        print(f"⏱️  Test completed in {duration:.2f} seconds")
        print("🚀 Multi-episode training working!")
        return 0
    else:
        print("❌ TRAINING PROGRESSION TEST FAILED")
        print("🔧 Please fix issues before running full training")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
