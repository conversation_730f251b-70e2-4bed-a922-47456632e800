"""
Advanced Money Management System for BlackJack Bot ML.

This module implements sophisticated money management strategies that go beyond
basic bankroll tracking, including streak detection, loss recovery, and
adaptive betting patterns for ML training optimization.
"""

import numpy as np
import math
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class StreakType(Enum):
    """Types of winning/losing streaks."""
    WIN_STREAK = "win_streak"
    LOSS_STREAK = "loss_streak"
    NEUTRAL = "neutral"


class BettingProgression(Enum):
    """Available betting progression strategies."""
    FLAT = "flat"
    MARTINGALE = "martingale"
    MODIFIED_MARTINGALE = "modified_martingale"
    FIBONACCI = "fibonacci"
    PAROLI = "paroli"
    LABOUCHERE = "labouchere"
    KELLY_ADAPTIVE = "kelly_adaptive"


@dataclass
class StreakInfo:
    """Information about current winning/losing streak."""
    type: StreakType = StreakType.NEUTRAL
    length: int = 0
    max_length: int = 0
    total_amount: float = 0.0
    start_bankroll: float = 0.0


@dataclass
class AdvancedMoneyConfig:
    """Configuration for advanced money management."""
    
    # Base configuration
    initial_bankroll: float = 1000.0
    base_bet_size: float = 10.0
    min_bet_size: float = 5.0
    max_bet_size: float = 100.0
    
    # Streak detection
    min_streak_length: int = 3
    max_streak_tracking: int = 20
    
    # Loss recovery strategies
    loss_recovery_enabled: bool = True
    max_recovery_attempts: int = 10
    recovery_multiplier: float = 1.5
    
    # Win streak capitalization
    win_streak_enabled: bool = True
    win_streak_multiplier: float = 1.25
    max_win_streak_bet: float = 50.0
    
    # Betting progression
    progression_strategy: BettingProgression = BettingProgression.MODIFIED_MARTINGALE
    progression_reset_threshold: int = 5
    
    # Risk management
    max_consecutive_losses: int = 10
    emergency_stop_loss: float = 0.3  # Stop at 30% bankroll loss
    profit_protection: float = 0.5  # Protect 50% of profits
    
    # Adaptive parameters
    confidence_threshold: float = 0.7
    volatility_adjustment: bool = True
    bankroll_percentage_betting: bool = True


@dataclass
class MoneyManagementMetrics:
    """Comprehensive metrics for money management performance."""
    
    # Current state
    current_bankroll: float = 0.0
    current_streak: StreakInfo = field(default_factory=StreakInfo)
    
    # Historical tracking
    bankroll_history: List[float] = field(default_factory=list)
    bet_history: List[float] = field(default_factory=list)
    result_history: List[bool] = field(default_factory=list)  # True = win, False = loss
    
    # Streak statistics
    win_streaks: List[int] = field(default_factory=list)
    loss_streaks: List[int] = field(default_factory=list)
    max_win_streak: int = 0
    max_loss_streak: int = 0
    
    # Recovery performance
    recovery_attempts: int = 0
    successful_recoveries: int = 0
    recovery_success_rate: float = 0.0
    
    # Progression performance
    progression_cycles: int = 0
    progression_profits: List[float] = field(default_factory=list)
    
    # Risk metrics
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    
    # Performance metrics
    total_hands: int = 0
    total_wagered: float = 0.0
    net_profit: float = 0.0
    roi: float = 0.0
    profit_factor: float = 0.0


class AdvancedMoneyManager:
    """
    Advanced money management system with sophisticated betting strategies.
    
    Implements streak detection, loss recovery, win capitalization, and
    adaptive betting patterns for optimal bankroll growth and preservation.
    """
    
    def __init__(self, config: AdvancedMoneyConfig):
        """Initialize the advanced money manager."""
        self.config = config
        self.metrics = MoneyManagementMetrics()
        self.metrics.current_bankroll = config.initial_bankroll
        
        # Progression state
        self.progression_sequence = []
        self.progression_index = 0
        self.in_recovery_mode = False
        self.recovery_target = 0.0
        
        # Initialize progression sequences
        self._initialize_progressions()
        
    def _initialize_progressions(self):
        """Initialize betting progression sequences."""
        if self.config.progression_strategy == BettingProgression.FIBONACCI:
            self.progression_sequence = self._generate_fibonacci_sequence(15)
        elif self.config.progression_strategy == BettingProgression.LABOUCHERE:
            self.progression_sequence = [1, 2, 3, 4, 5]  # Starting sequence
        else:
            self.progression_sequence = [1, 2, 4, 8, 16, 32]  # Martingale-based
            
    def _generate_fibonacci_sequence(self, length: int) -> List[int]:
        """Generate Fibonacci sequence for betting progression."""
        sequence = [1, 1]
        for i in range(2, length):
            sequence.append(sequence[i-1] + sequence[i-2])
        return sequence
        
    def calculate_bet_size(self, model_confidence: float = 0.5, 
                          estimated_edge: float = 0.0,
                          game_context: Dict[str, Any] = None) -> float:
        """
        Calculate optimal bet size using advanced money management.
        
        Args:
            model_confidence: ML model confidence (0-1)
            estimated_edge: Estimated edge for this hand
            game_context: Additional game context (hand count, etc.)
            
        Returns:
            Optimal bet size considering all factors
        """
        # Base bet calculation
        base_bet = self._calculate_base_bet()
        
        # Apply streak-based adjustments
        streak_multiplier = self._calculate_streak_multiplier()
        
        # Apply progression strategy
        progression_multiplier = self._calculate_progression_multiplier()
        
        # Apply confidence adjustments
        confidence_multiplier = self._calculate_confidence_multiplier(model_confidence)
        
        # Apply edge-based adjustments
        edge_multiplier = self._calculate_edge_multiplier(estimated_edge)
        
        # Combine all factors
        bet_size = (base_bet * 
                   streak_multiplier * 
                   progression_multiplier * 
                   confidence_multiplier * 
                   edge_multiplier)
        
        # Apply constraints and risk management
        bet_size = self._apply_risk_constraints(bet_size)
        
        return bet_size
        
    def _calculate_base_bet(self) -> float:
        """Calculate base bet size."""
        if self.config.bankroll_percentage_betting:
            # Percentage of current bankroll
            return self.metrics.current_bankroll * (self.config.base_bet_size / self.config.initial_bankroll)
        else:
            # Fixed base bet
            return self.config.base_bet_size
            
    def _calculate_streak_multiplier(self) -> float:
        """Calculate betting multiplier based on current streak."""
        streak = self.metrics.current_streak
        
        if streak.type == StreakType.WIN_STREAK and self.config.win_streak_enabled:
            # Increase bets during win streaks (Paroli-style)
            if streak.length >= self.config.min_streak_length:
                multiplier = min(self.config.win_streak_multiplier ** (streak.length - 2), 3.0)
                return multiplier
                
        elif streak.type == StreakType.LOSS_STREAK and self.config.loss_recovery_enabled:
            # Controlled increase during loss streaks
            if streak.length >= self.config.min_streak_length:
                multiplier = min(self.config.recovery_multiplier ** (streak.length - 2), 2.0)
                return multiplier
                
        return 1.0
        
    def _calculate_progression_multiplier(self) -> float:
        """Calculate multiplier based on betting progression strategy."""
        if self.config.progression_strategy == BettingProgression.FLAT:
            return 1.0
            
        elif self.config.progression_strategy == BettingProgression.MARTINGALE:
            if self.metrics.current_streak.type == StreakType.LOSS_STREAK:
                return 2.0 ** min(self.metrics.current_streak.length, 5)
            return 1.0
            
        elif self.config.progression_strategy == BettingProgression.MODIFIED_MARTINGALE:
            if self.metrics.current_streak.type == StreakType.LOSS_STREAK:
                return min(1.5 ** self.metrics.current_streak.length, 4.0)
            return 1.0
            
        elif self.config.progression_strategy == BettingProgression.FIBONACCI:
            if self.progression_index < len(self.progression_sequence):
                return self.progression_sequence[self.progression_index]
            return self.progression_sequence[-1]
            
        elif self.config.progression_strategy == BettingProgression.PAROLI:
            if self.metrics.current_streak.type == StreakType.WIN_STREAK:
                return min(2.0 ** min(self.metrics.current_streak.length, 3), 8.0)
            return 1.0
            
        return 1.0
        
    def _calculate_confidence_multiplier(self, confidence: float) -> float:
        """Calculate multiplier based on ML model confidence."""
        if confidence > self.config.confidence_threshold:
            # Increase bet size for high-confidence predictions
            excess_confidence = confidence - self.config.confidence_threshold
            multiplier = 1.0 + (excess_confidence * 2.0)  # Up to 60% increase
            return min(multiplier, 1.6)
        elif confidence < 0.4:
            # Decrease bet size for low-confidence predictions
            return max(0.5, confidence * 2.0)
        return 1.0
        
    def _calculate_edge_multiplier(self, edge: float) -> float:
        """Calculate multiplier based on estimated edge."""
        if edge > 0.01:  # Positive edge
            return min(1.0 + (edge * 10.0), 1.5)
        elif edge < -0.01:  # Negative edge
            return max(0.7, 1.0 + (edge * 5.0))
        return 1.0
        
    def _apply_risk_constraints(self, bet_size: float) -> float:
        """Apply risk management constraints to bet size."""
        # Minimum bet
        bet_size = max(bet_size, self.config.min_bet_size)
        
        # Maximum bet (absolute)
        bet_size = min(bet_size, self.config.max_bet_size)
        
        # Maximum bet (percentage of bankroll)
        max_percentage_bet = self.metrics.current_bankroll * 0.1  # 10% max
        bet_size = min(bet_size, max_percentage_bet)
        
        # Emergency constraints during large drawdowns
        if self.metrics.current_drawdown > 0.2:  # 20% drawdown
            bet_size = min(bet_size, self.config.min_bet_size * 2)
            
        # Ensure we don't bet more than we have
        bet_size = min(bet_size, self.metrics.current_bankroll)
        
        return bet_size
        
    def update_result(self, bet_amount: float, won: bool, 
                     payout_multiplier: float = 1.0) -> Dict[str, Any]:
        """
        Update money management state with hand result.
        
        Args:
            bet_amount: Amount wagered
            won: Whether the hand was won
            payout_multiplier: Payout multiplier (1.5 for blackjack, etc.)
            
        Returns:
            Updated metrics and recommendations
        """
        # Update basic metrics
        self.metrics.total_hands += 1
        self.metrics.total_wagered += bet_amount
        self.metrics.bet_history.append(bet_amount)
        self.metrics.result_history.append(won)
        
        # Update bankroll
        if won:
            winnings = bet_amount * payout_multiplier
            self.metrics.current_bankroll += winnings
            self.metrics.net_profit += winnings
        else:
            self.metrics.current_bankroll -= bet_amount
            self.metrics.net_profit -= bet_amount
            
        self.metrics.bankroll_history.append(self.metrics.current_bankroll)
        
        # Update streak information
        self._update_streak_info(won)
        
        # Update progression state
        self._update_progression_state(won)
        
        # Update performance metrics
        self._update_performance_metrics()
        
        # Check for emergency conditions
        emergency_action = self._check_emergency_conditions()
        
        return {
            "current_bankroll": self.metrics.current_bankroll,
            "current_streak": self.metrics.current_streak,
            "net_profit": self.metrics.net_profit,
            "roi": self.metrics.roi,
            "max_drawdown": self.metrics.max_drawdown,
            "emergency_action": emergency_action,
            "next_bet_recommendation": self.calculate_bet_size()
        }
        
    def _update_streak_info(self, won: bool):
        """Update current streak information."""
        current_streak = self.metrics.current_streak
        
        if won:
            if current_streak.type == StreakType.WIN_STREAK:
                current_streak.length += 1
                current_streak.total_amount += self.metrics.bet_history[-1]
            else:
                # End loss streak, start win streak
                if current_streak.type == StreakType.LOSS_STREAK and current_streak.length > 0:
                    self.metrics.loss_streaks.append(current_streak.length)
                    self.metrics.max_loss_streak = max(self.metrics.max_loss_streak, current_streak.length)
                
                current_streak.type = StreakType.WIN_STREAK
                current_streak.length = 1
                current_streak.total_amount = self.metrics.bet_history[-1]
                current_streak.start_bankroll = self.metrics.bankroll_history[-2] if len(self.metrics.bankroll_history) > 1 else self.config.initial_bankroll
        else:
            if current_streak.type == StreakType.LOSS_STREAK:
                current_streak.length += 1
                current_streak.total_amount += self.metrics.bet_history[-1]
            else:
                # End win streak, start loss streak
                if current_streak.type == StreakType.WIN_STREAK and current_streak.length > 0:
                    self.metrics.win_streaks.append(current_streak.length)
                    self.metrics.max_win_streak = max(self.metrics.max_win_streak, current_streak.length)
                
                current_streak.type = StreakType.LOSS_STREAK
                current_streak.length = 1
                current_streak.total_amount = self.metrics.bet_history[-1]
                current_streak.start_bankroll = self.metrics.bankroll_history[-2] if len(self.metrics.bankroll_history) > 1 else self.config.initial_bankroll
                
        current_streak.max_length = max(current_streak.max_length, current_streak.length)
        
    def _update_progression_state(self, won: bool):
        """Update betting progression state."""
        if self.config.progression_strategy == BettingProgression.FIBONACCI:
            if won:
                self.progression_index = max(0, self.progression_index - 2)
            else:
                self.progression_index = min(len(self.progression_sequence) - 1, self.progression_index + 1)
                
        elif self.config.progression_strategy == BettingProgression.LABOUCHERE:
            if won and len(self.progression_sequence) > 0:
                # Remove first and last numbers
                if len(self.progression_sequence) > 1:
                    self.progression_sequence = self.progression_sequence[1:-1]
                else:
                    self.progression_sequence = []
            elif not won:
                # Add the bet amount to the end
                bet_units = int(self.metrics.bet_history[-1] / self.config.base_bet_size)
                self.progression_sequence.append(bet_units)
                
    def _update_performance_metrics(self):
        """Update comprehensive performance metrics."""
        if len(self.metrics.bankroll_history) < 2:
            return
            
        # ROI calculation
        self.metrics.roi = (self.metrics.current_bankroll - self.config.initial_bankroll) / self.config.initial_bankroll
        
        # Drawdown calculation
        peak_bankroll = max(self.metrics.bankroll_history)
        current_drawdown = (peak_bankroll - self.metrics.current_bankroll) / peak_bankroll
        self.metrics.current_drawdown = current_drawdown
        self.metrics.max_drawdown = max(self.metrics.max_drawdown, current_drawdown)
        
        # Volatility calculation (rolling standard deviation)
        if len(self.metrics.bankroll_history) >= 20:
            recent_returns = []
            for i in range(1, min(21, len(self.metrics.bankroll_history))):
                prev_bankroll = self.metrics.bankroll_history[-i-1]
                curr_bankroll = self.metrics.bankroll_history[-i]
                if prev_bankroll > 0:
                    return_rate = (curr_bankroll - prev_bankroll) / prev_bankroll
                    recent_returns.append(return_rate)
            
            if recent_returns:
                self.metrics.volatility = np.std(recent_returns)
                
                # Sharpe ratio (simplified)
                avg_return = np.mean(recent_returns)
                if self.metrics.volatility > 0:
                    self.metrics.sharpe_ratio = avg_return / self.metrics.volatility
                    
        # Profit factor
        wins = [bet for i, bet in enumerate(self.metrics.bet_history) if i < len(self.metrics.result_history) and self.metrics.result_history[i]]
        losses = [bet for i, bet in enumerate(self.metrics.bet_history) if i < len(self.metrics.result_history) and not self.metrics.result_history[i]]
        
        total_wins = sum(wins) if wins else 0
        total_losses = sum(losses) if losses else 1  # Avoid division by zero
        
        self.metrics.profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
    def _check_emergency_conditions(self) -> Optional[str]:
        """Check for emergency conditions requiring immediate action."""
        # Emergency stop loss
        if self.metrics.current_drawdown >= self.config.emergency_stop_loss:
            return "emergency_stop_loss"
            
        # Maximum consecutive losses
        if (self.metrics.current_streak.type == StreakType.LOSS_STREAK and 
            self.metrics.current_streak.length >= self.config.max_consecutive_losses):
            return "max_consecutive_losses"
            
        # Bankroll too low for minimum bet
        if self.metrics.current_bankroll < self.config.min_bet_size:
            return "insufficient_bankroll"
            
        return None
        
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Get comprehensive money management performance report."""
        return {
            "current_state": {
                "bankroll": self.metrics.current_bankroll,
                "roi": self.metrics.roi,
                "net_profit": self.metrics.net_profit,
                "total_hands": self.metrics.total_hands
            },
            "streak_analysis": {
                "current_streak": self.metrics.current_streak,
                "max_win_streak": self.metrics.max_win_streak,
                "max_loss_streak": self.metrics.max_loss_streak,
                "avg_win_streak": np.mean(self.metrics.win_streaks) if self.metrics.win_streaks else 0,
                "avg_loss_streak": np.mean(self.metrics.loss_streaks) if self.metrics.loss_streaks else 0
            },
            "risk_metrics": {
                "max_drawdown": self.metrics.max_drawdown,
                "current_drawdown": self.metrics.current_drawdown,
                "volatility": self.metrics.volatility,
                "sharpe_ratio": self.metrics.sharpe_ratio
            },
            "progression_performance": {
                "strategy": self.config.progression_strategy.value,
                "cycles_completed": self.metrics.progression_cycles,
                "profit_factor": self.metrics.profit_factor
            }
        }
