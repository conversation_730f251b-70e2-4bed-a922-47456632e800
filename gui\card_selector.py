"""
Card Selection Widget for BlackJack Bot ML GUI.

This module implements the button-based card selection interface with
13 rank buttons (A, 2-10, J, Q, K) for dealer and player card selection.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Callable, Optional
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.card import Card, Rank, Suit


class CardSelectorWidget:
    """
    Widget for selecting blackjack cards using rank buttons.
    
    Provides separate interfaces for dealer and player card selection
    with visual feedback and validation.
    """
    
    def __init__(self, parent, on_selection_change: Optional[Callable] = None):
        """
        Initialize the card selector widget.
        
        Args:
            parent: Parent tkinter widget
            on_selection_change: Callback function for selection changes
        """
        self.parent = parent
        self.on_selection_change = on_selection_change
        
        # Card selection state
        self.dealer_cards = []
        self.player_cards = []
        self.max_player_cards = 10  # Reasonable limit for display
        
        # Available ranks for selection
        self.ranks = [
            Rank.ACE, Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE,
            Rank.SIX, Rank.SEVEN, Rank.EIGHT, Rank.NINE, Rank.TEN,
            Rank.JACK, Rank.QUEEN, Rank.KING
        ]
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create all card selection widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        
        # Dealer card selection
        self.create_dealer_section()
        
        # Player card selection  
        self.create_player_section()
        
        # Control buttons
        self.create_control_buttons()
        
    def create_dealer_section(self):
        """Create dealer card selection section."""
        self.dealer_frame = ttk.LabelFrame(
            self.main_frame,
            text="Dealer Upcard",
            padding="10"
        )
        
        # Dealer instruction
        self.dealer_instruction = ttk.Label(
            self.dealer_frame,
            text="Select dealer's face-up card:",
            font=('Arial', 10)
        )
        
        # Dealer rank buttons frame
        self.dealer_buttons_frame = ttk.Frame(self.dealer_frame)
        
        # Create dealer rank buttons
        self.dealer_buttons = {}
        for i, rank in enumerate(self.ranks):
            btn = ttk.Button(
                self.dealer_buttons_frame,
                text=self.get_rank_display(rank),
                style='Card.TButton',
                command=lambda r=rank: self.select_dealer_card(r),
                width=4
            )
            self.dealer_buttons[rank] = btn
            
        # Selected dealer card display
        self.dealer_selected_frame = ttk.Frame(self.dealer_frame)
        self.dealer_selected_label = ttk.Label(
            self.dealer_selected_frame,
            text="Selected: None",
            font=('Arial', 10, 'bold'),
            foreground='blue'
        )
        
    def create_player_section(self):
        """Create player card selection section."""
        self.player_frame = ttk.LabelFrame(
            self.main_frame,
            text="Player Cards",
            padding="10"
        )
        
        # Player instruction
        self.player_instruction = ttk.Label(
            self.player_frame,
            text="Select player cards (click multiple times for multiple cards):",
            font=('Arial', 10)
        )
        
        # Player rank buttons frame
        self.player_buttons_frame = ttk.Frame(self.player_frame)
        
        # Create player rank buttons
        self.player_buttons = {}
        for i, rank in enumerate(self.ranks):
            btn = ttk.Button(
                self.player_buttons_frame,
                text=self.get_rank_display(rank),
                style='Card.TButton',
                command=lambda r=rank: self.add_player_card(r),
                width=4
            )
            self.player_buttons[rank] = btn
            
        # Selected player cards display
        self.player_selected_frame = ttk.Frame(self.player_frame)
        self.player_selected_label = ttk.Label(
            self.player_selected_frame,
            text="Selected: None",
            font=('Arial', 10, 'bold'),
            foreground='green'
        )
        
        # Player cards count
        self.player_count_label = ttk.Label(
            self.player_selected_frame,
            text="Cards: 0",
            font=('Arial', 9),
            foreground='gray'
        )
        
    def create_control_buttons(self):
        """Create control buttons for reset and clear operations."""
        self.control_frame = ttk.Frame(self.main_frame)
        
        # Clear dealer button
        self.clear_dealer_btn = ttk.Button(
            self.control_frame,
            text="Clear Dealer",
            command=self.clear_dealer,
            style='Action.TButton'
        )
        
        # Clear player button
        self.clear_player_btn = ttk.Button(
            self.control_frame,
            text="Clear Player",
            command=self.clear_player,
            style='Action.TButton'
        )
        
        # Reset all button
        self.reset_all_btn = ttk.Button(
            self.control_frame,
            text="Reset All",
            command=self.reset,
            style='Action.TButton'
        )
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure grid weights
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Dealer section
        self.dealer_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        self.dealer_instruction.grid(row=0, column=0, sticky='w', pady=(0, 10))
        self.dealer_buttons_frame.grid(row=1, column=0, sticky='ew')
        self.dealer_selected_frame.grid(row=2, column=0, sticky='ew', pady=(10, 0))
        self.dealer_selected_label.grid(row=0, column=0, sticky='w')
        
        # Layout dealer buttons in grid
        for i, rank in enumerate(self.ranks):
            row = i // 4
            col = i % 4
            self.dealer_buttons[rank].grid(row=row, column=col, padx=2, pady=2, sticky='ew')
            
        # Configure dealer buttons frame columns
        for i in range(4):
            self.dealer_buttons_frame.grid_columnconfigure(i, weight=1)
            
        # Player section
        self.player_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
        self.player_instruction.grid(row=0, column=0, sticky='w', pady=(0, 10))
        self.player_buttons_frame.grid(row=1, column=0, sticky='ew')
        self.player_selected_frame.grid(row=2, column=0, sticky='ew', pady=(10, 0))
        self.player_selected_label.grid(row=0, column=0, sticky='w')
        self.player_count_label.grid(row=1, column=0, sticky='w')
        
        # Layout player buttons in grid
        for i, rank in enumerate(self.ranks):
            row = i // 4
            col = i % 4
            self.player_buttons[rank].grid(row=row, column=col, padx=2, pady=2, sticky='ew')
            
        # Configure player buttons frame columns
        for i in range(4):
            self.player_buttons_frame.grid_columnconfigure(i, weight=1)
            
        # Control buttons
        self.control_frame.grid(row=2, column=0, sticky='ew')
        self.clear_dealer_btn.grid(row=0, column=0, padx=(0, 5), pady=5, sticky='ew')
        self.clear_player_btn.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        self.reset_all_btn.grid(row=0, column=2, padx=(5, 0), pady=5, sticky='ew')
        
        # Configure control frame columns
        for i in range(3):
            self.control_frame.grid_columnconfigure(i, weight=1)
            
    def get_rank_display(self, rank: Rank) -> str:
        """Get display text for a rank."""
        if rank == Rank.ACE:
            return "A"
        elif rank == Rank.JACK:
            return "J"
        elif rank == Rank.QUEEN:
            return "Q"
        elif rank == Rank.KING:
            return "K"
        else:
            return str(rank.value)
            
    def select_dealer_card(self, rank: Rank):
        """Select a dealer card."""
        # Create card with default suit (suits don't matter for basic strategy)
        card = Card(Suit.SPADES, rank)
        self.dealer_cards = [card]  # Dealer only has one upcard
        
        # Update display
        self.update_dealer_display()
        
        # Update button states
        self.update_dealer_button_states()
        
        # Notify callback
        self.notify_selection_change()
        
    def add_player_card(self, rank: Rank):
        """Add a player card."""
        if len(self.player_cards) >= self.max_player_cards:
            return  # Don't add more cards than limit
            
        # Create card with default suit
        card = Card(Suit.HEARTS, rank)
        self.player_cards.append(card)
        
        # Update display
        self.update_player_display()
        
        # Update button states
        self.update_player_button_states()
        
        # Notify callback
        self.notify_selection_change()
        
    def clear_dealer(self):
        """Clear dealer card selection."""
        self.dealer_cards = []
        self.update_dealer_display()
        self.update_dealer_button_states()
        self.notify_selection_change()
        
    def clear_player(self):
        """Clear player card selection."""
        self.player_cards = []
        self.update_player_display()
        self.update_player_button_states()
        self.notify_selection_change()
        
    def reset(self):
        """Reset all card selections."""
        self.clear_dealer()
        self.clear_player()
        
    def update_dealer_display(self):
        """Update dealer card display."""
        if self.dealer_cards:
            card = self.dealer_cards[0]
            display_text = f"Selected: {card.rank.name} ({self.get_rank_display(card.rank)})"
        else:
            display_text = "Selected: None"
            
        self.dealer_selected_label.config(text=display_text)
        
    def update_player_display(self):
        """Update player cards display."""
        if self.player_cards:
            card_displays = [self.get_rank_display(card.rank) for card in self.player_cards]
            display_text = f"Selected: {', '.join(card_displays)}"
        else:
            display_text = "Selected: None"
            
        self.player_selected_label.config(text=display_text)
        self.player_count_label.config(text=f"Cards: {len(self.player_cards)}")
        
    def update_dealer_button_states(self):
        """Update dealer button visual states."""
        for rank, button in self.dealer_buttons.items():
            if self.dealer_cards and self.dealer_cards[0].rank == rank:
                button.state(['pressed'])
            else:
                button.state(['!pressed'])
                
    def update_player_button_states(self):
        """Update player button visual states."""
        # Count occurrences of each rank
        rank_counts = {}
        for card in self.player_cards:
            rank_counts[card.rank] = rank_counts.get(card.rank, 0) + 1
            
        # Update button text to show count
        for rank, button in self.player_buttons.items():
            count = rank_counts.get(rank, 0)
            base_text = self.get_rank_display(rank)
            if count > 0:
                button.config(text=f"{base_text} ({count})")
                button.state(['pressed'])
            else:
                button.config(text=base_text)
                button.state(['!pressed'])
                
    def notify_selection_change(self):
        """Notify callback of selection changes."""
        if self.on_selection_change:
            self.on_selection_change(self.dealer_cards.copy(), self.player_cards.copy())
            
    def get_dealer_cards(self) -> List[Card]:
        """Get current dealer cards."""
        return self.dealer_cards.copy()
        
    def get_player_cards(self) -> List[Card]:
        """Get current player cards."""
        return self.player_cards.copy()
        
    def set_cards(self, dealer_cards: List[Card], player_cards: List[Card]):
        """Set card selections programmatically."""
        self.dealer_cards = dealer_cards.copy()
        self.player_cards = player_cards.copy()
        
        self.update_dealer_display()
        self.update_player_display()
        self.update_dealer_button_states()
        self.update_player_button_states()
        
        self.notify_selection_change()
