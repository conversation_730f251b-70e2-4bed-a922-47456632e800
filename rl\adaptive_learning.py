"""
Adaptive Learning System for BlackJack Bot ML.

This module implements adaptive learning algorithms that can adjust
strategies based on environment feedback while maintaining human-like
characteristics and avoiding detection.
"""

import numpy as np
import random
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Union
from enum import Enum

from .base_rl_agent import BaseRLAgent
from .evasion_manager import Evasion<PERSON>anager
from personas.base_persona import BasePersona, DecisionContext
from core.game_logic import GameA<PERSON>, GameState


class AdaptationTrigger(Enum):
    """Triggers for adaptive learning adjustments."""
    PERFORMANCE_DECLINE = "performance_decline"
    DETECTION_RISK = "detection_risk"
    ENVIRONMENT_CHANGE = "environment_change"
    EXPLORATION_NEED = "exploration_need"
    PERSONA_MISMATCH = "persona_mismatch"
    CONSISTENCY_DRIFT = "consistency_drift"


@dataclass
class AdaptationConfig:
    """Configuration for adaptive learning system."""
    
    # Performance monitoring
    performance_window: int = 100
    performance_threshold: float = -0.1  # Decline threshold
    min_adaptation_interval: int = 50
    
    # Learning rate adaptation
    lr_adaptation_enabled: bool = True
    lr_increase_factor: float = 1.1
    lr_decrease_factor: float = 0.9
    min_learning_rate: float = 1e-5
    max_learning_rate: float = 1e-2
    
    # Exploration adaptation
    exploration_adaptation_enabled: bool = True
    epsilon_increase_factor: float = 1.2
    epsilon_decrease_factor: float = 0.8
    min_epsilon_adaptive: float = 0.01
    max_epsilon_adaptive: float = 0.5
    
    # Persona adaptation
    persona_adaptation_enabled: bool = True
    persona_mismatch_threshold: float = 0.3
    persona_adaptation_strength: float = 0.1
    
    # Evasion adaptation
    evasion_adaptation_enabled: bool = True
    detection_risk_threshold: float = 0.8
    evasion_intensity_factor: float = 1.5
    
    # Consistency adaptation
    consistency_adaptation_enabled: bool = True
    target_consistency_range: Tuple[float, float] = (0.7, 0.85)
    consistency_adaptation_rate: float = 0.05


@dataclass
class AdaptationMetrics:
    """Metrics for tracking adaptation performance."""
    total_adaptations: int = 0
    adaptation_triggers: Dict[AdaptationTrigger, int] = field(default_factory=dict)
    performance_history: List[float] = field(default_factory=list)
    adaptation_effectiveness: float = 0.0
    last_adaptation_step: int = 0
    
    def __post_init__(self):
        """Initialize default values."""
        if not self.adaptation_triggers:
            self.adaptation_triggers = {trigger: 0 for trigger in AdaptationTrigger}


class BaseAdaptationStrategy(ABC):
    """Abstract base class for adaptation strategies."""
    
    def __init__(self, config: AdaptationConfig):
        """Initialize adaptation strategy."""
        self.config = config
        self.activation_count = 0
        self.last_activation = 0
        self.effectiveness_history = []
    
    @abstractmethod
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Determine if adaptation should be triggered."""
        pass
    
    @abstractmethod
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Apply adaptation and return metadata."""
        pass
    
    def get_effectiveness_score(self) -> float:
        """Get effectiveness score for this strategy."""
        if not self.effectiveness_history:
            return 0.5  # Neutral
        return sum(self.effectiveness_history) / len(self.effectiveness_history)


class LearningRateAdaptation(BaseAdaptationStrategy):
    """Adaptive learning rate adjustment based on performance."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if learning rate should be adapted."""
        if not self.config.lr_adaptation_enabled:
            return False
        
        # Check performance trend
        if len(metrics.get('performance_history', [])) < self.config.performance_window:
            return False
        
        recent_performance = metrics['performance_history'][-self.config.performance_window:]
        performance_trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
        
        # Adapt if performance is declining or stagnating
        return performance_trend < self.config.performance_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt learning rate based on performance."""
        old_lr = agent.optimizer.param_groups[0]['lr']
        
        if trigger == AdaptationTrigger.PERFORMANCE_DECLINE:
            # Increase learning rate to escape local minimum
            new_lr = min(old_lr * self.config.lr_increase_factor, self.config.max_learning_rate)
        else:
            # Decrease learning rate for stability
            new_lr = max(old_lr * self.config.lr_decrease_factor, self.config.min_learning_rate)
        
        # Update learning rate
        for param_group in agent.optimizer.param_groups:
            param_group['lr'] = new_lr
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "learning_rate_adaptation",
            "old_lr": old_lr,
            "new_lr": new_lr,
            "trigger": trigger.value,
            "adaptation_factor": new_lr / old_lr
        }


class ExplorationAdaptation(BaseAdaptationStrategy):
    """Adaptive exploration (epsilon) adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if exploration should be adapted."""
        if not self.config.exploration_adaptation_enabled:
            return False
        
        # Check if stuck in local optimum or need more exploration
        detection_risk = metrics.get('detection_risk', 0.0)
        performance_variance = metrics.get('performance_variance', 0.0)
        
        # Need more exploration if high detection risk or low performance variance
        return detection_risk > 0.7 or performance_variance < 0.1
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt exploration rate."""
        old_epsilon = agent.epsilon
        
        if trigger in [AdaptationTrigger.DETECTION_RISK, AdaptationTrigger.EXPLORATION_NEED]:
            # Increase exploration
            new_epsilon = min(old_epsilon * self.config.epsilon_increase_factor, 
                            self.config.max_epsilon_adaptive)
        else:
            # Decrease exploration
            new_epsilon = max(old_epsilon * self.config.epsilon_decrease_factor,
                            self.config.min_epsilon_adaptive)
        
        agent.epsilon = new_epsilon
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "exploration_adaptation",
            "old_epsilon": old_epsilon,
            "new_epsilon": new_epsilon,
            "trigger": trigger.value,
            "adaptation_factor": new_epsilon / old_epsilon if old_epsilon > 0 else 1.0
        }


class PersonaAdaptation(BaseAdaptationStrategy):
    """Adaptive persona behavior adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if persona adaptation is needed."""
        if not self.config.persona_adaptation_enabled or not hasattr(agent, 'persona'):
            return False
        
        # Check persona-RL alignment
        persona_rl_mismatch = metrics.get('persona_rl_mismatch', 0.0)
        return persona_rl_mismatch > self.config.persona_mismatch_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt persona behavior to better align with RL learning."""
        if not hasattr(agent, 'persona') or not agent.persona:
            return {"strategy": "persona_adaptation", "result": "no_persona"}
        
        # Adjust persona parameters to better align with RL
        old_accuracy = agent.persona.current_accuracy
        old_noise = getattr(agent.persona, 'behavioral_noise', 0.1)
        
        if trigger == AdaptationTrigger.PERSONA_MISMATCH:
            # Increase persona accuracy to align better with RL
            new_accuracy = min(old_accuracy + self.config.persona_adaptation_strength, 0.98)
            agent.persona.current_accuracy = new_accuracy
            
            # Adjust behavioral noise
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level *= 0.9  # Reduce noise
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "persona_adaptation",
            "old_accuracy": old_accuracy,
            "new_accuracy": agent.persona.current_accuracy,
            "trigger": trigger.value,
            "adaptation_strength": self.config.persona_adaptation_strength
        }


class EvasionAdaptation(BaseAdaptationStrategy):
    """Adaptive evasion strategy adjustment."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if evasion adaptation is needed."""
        if not self.config.evasion_adaptation_enabled:
            return False
        
        detection_risk = metrics.get('detection_risk', 0.0)
        return detection_risk > self.config.detection_risk_threshold
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt evasion strategies."""
        if not hasattr(agent, 'evasion_manager'):
            return {"strategy": "evasion_adaptation", "result": "no_evasion_manager"}
        
        evasion_manager = agent.evasion_manager
        old_config = evasion_manager.config
        
        # Increase evasion intensity
        new_noise_intensity = min(old_config.noise_intensity * self.config.evasion_intensity_factor, 0.5)
        new_disruption_prob = min(old_config.disruption_probability * self.config.evasion_intensity_factor, 0.2)
        
        # Update evasion configuration
        from .evasion_strategies import EvasionConfig
        new_config = EvasionConfig(
            consistency_threshold=old_config.consistency_threshold,
            noise_intensity=new_noise_intensity,
            disruption_probability=new_disruption_prob,
            technique_weights=old_config.technique_weights.copy()
        )
        
        evasion_manager.update_config(new_config)
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "evasion_adaptation",
            "old_noise_intensity": old_config.noise_intensity,
            "new_noise_intensity": new_noise_intensity,
            "old_disruption_prob": old_config.disruption_probability,
            "new_disruption_prob": new_disruption_prob,
            "trigger": trigger.value
        }


class ConsistencyAdaptation(BaseAdaptationStrategy):
    """Adaptive consistency management."""
    
    def should_adapt(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check if consistency adaptation is needed."""
        if not self.config.consistency_adaptation_enabled:
            return False
        
        current_consistency = metrics.get('current_consistency', 0.0)
        target_min, target_max = self.config.target_consistency_range
        
        return current_consistency < target_min or current_consistency > target_max
    
    def apply_adaptation(self, agent: BaseRLAgent, trigger: AdaptationTrigger) -> Dict[str, Any]:
        """Adapt consistency levels."""
        current_consistency = agent.consistency_scores[-1] if agent.consistency_scores else 0.0
        target_min, target_max = self.config.target_consistency_range
        
        adaptation_applied = False
        
        if current_consistency > target_max:
            # Too consistent - increase randomness
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level = min(
                        noise_strategy.current_noise_level * 1.2, 0.3
                    )
                    adaptation_applied = True
        
        elif current_consistency < target_min:
            # Too inconsistent - reduce randomness
            if hasattr(agent, 'evasion_manager'):
                noise_strategy = agent.evasion_manager.strategies.get('behavioral_noise')
                if noise_strategy:
                    noise_strategy.current_noise_level = max(
                        noise_strategy.current_noise_level * 0.8, 0.01
                    )
                    adaptation_applied = True
        
        self.activation_count += 1
        self.last_activation = agent.training_step
        
        return {
            "strategy": "consistency_adaptation",
            "current_consistency": current_consistency,
            "target_range": self.config.target_consistency_range,
            "adaptation_applied": adaptation_applied,
            "trigger": trigger.value
        }


class AdaptiveLearningSystem:
    """
    Comprehensive adaptive learning system.
    
    Coordinates multiple adaptation strategies to optimize learning
    while maintaining human-like behavior and evasion capabilities.
    """
    
    def __init__(self, config: AdaptationConfig):
        """Initialize adaptive learning system."""
        self.config = config
        self.metrics = AdaptationMetrics()
        
        # Initialize adaptation strategies
        self.strategies = {
            "learning_rate": LearningRateAdaptation(config),
            "exploration": ExplorationAdaptation(config),
            "persona": PersonaAdaptation(config),
            "evasion": EvasionAdaptation(config),
            "consistency": ConsistencyAdaptation(config)
        }
        
        # Adaptation history
        self.adaptation_history = []
        self.performance_baseline = None
        
    def update(self, agent: BaseRLAgent, current_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Update adaptive learning system and apply adaptations if needed.
        
        Args:
            agent: RL agent to adapt
            current_metrics: Current performance and behavior metrics
            
        Returns:
            List of applied adaptations
        """
        applied_adaptations = []
        
        # Check if minimum interval has passed
        if (agent.training_step - self.metrics.last_adaptation_step < 
            self.config.min_adaptation_interval):
            return applied_adaptations
        
        # Determine adaptation triggers
        triggers = self._identify_adaptation_triggers(agent, current_metrics)
        
        # Apply adaptations based on triggers
        for trigger in triggers:
            adaptations = self._apply_adaptations_for_trigger(agent, trigger)
            applied_adaptations.extend(adaptations)
        
        # Update metrics
        if applied_adaptations:
            self.metrics.total_adaptations += len(applied_adaptations)
            self.metrics.last_adaptation_step = agent.training_step

            # Record adaptation event
            adaptation_event = {
                "timestamp": time.time(),
                "training_step": agent.training_step,
                "triggers": [t.value for t in triggers],
                "adaptations": applied_adaptations,
                "pre_adaptation_metrics": current_metrics.copy()
            }

            self.adaptation_history.append(adaptation_event)

            # Calculate and update effectiveness for recent adaptations
            self._update_adaptation_effectiveness(current_metrics)

            # Keep history manageable
            if len(self.adaptation_history) > 100:
                self.adaptation_history.pop(0)
        
        return applied_adaptations
    
    def _identify_adaptation_triggers(self, agent: BaseRLAgent, 
                                    metrics: Dict[str, Any]) -> List[AdaptationTrigger]:
        """Identify which adaptation triggers are active."""
        triggers = []
        
        # Performance decline trigger
        if self._check_performance_decline(metrics):
            triggers.append(AdaptationTrigger.PERFORMANCE_DECLINE)
        
        # Detection risk trigger
        detection_risk = metrics.get('detection_risk', 0.0)
        if detection_risk > self.config.detection_risk_threshold:
            triggers.append(AdaptationTrigger.DETECTION_RISK)
        
        # Exploration need trigger
        if self._check_exploration_need(metrics):
            triggers.append(AdaptationTrigger.EXPLORATION_NEED)
        
        # Persona mismatch trigger
        if self._check_persona_mismatch(agent, metrics):
            triggers.append(AdaptationTrigger.PERSONA_MISMATCH)
        
        # Consistency drift trigger
        if self._check_consistency_drift(metrics):
            triggers.append(AdaptationTrigger.CONSISTENCY_DRIFT)
        
        return triggers
    
    def _check_performance_decline(self, metrics: Dict[str, Any]) -> bool:
        """Check for performance decline."""
        performance_history = metrics.get('performance_history', [])
        
        if len(performance_history) < self.config.performance_window:
            return False
        
        recent_performance = performance_history[-self.config.performance_window:]
        trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
        
        return trend < self.config.performance_threshold
    
    def _check_exploration_need(self, metrics: Dict[str, Any]) -> bool:
        """Check if more exploration is needed."""
        performance_variance = metrics.get('performance_variance', 0.0)
        return performance_variance < 0.1
    
    def _check_persona_mismatch(self, agent: BaseRLAgent, metrics: Dict[str, Any]) -> bool:
        """Check for persona-RL mismatch."""
        if not hasattr(agent, 'persona') or not agent.persona:
            return False
        
        persona_rl_mismatch = metrics.get('persona_rl_mismatch', 0.0)
        return persona_rl_mismatch > self.config.persona_mismatch_threshold
    
    def _check_consistency_drift(self, metrics: Dict[str, Any]) -> bool:
        """Check for consistency drift."""
        current_consistency = metrics.get('current_consistency', 0.0)
        target_min, target_max = self.config.target_consistency_range
        
        return current_consistency < target_min or current_consistency > target_max
    
    def _apply_adaptations_for_trigger(self, agent: BaseRLAgent, 
                                     trigger: AdaptationTrigger) -> List[Dict[str, Any]]:
        """Apply adaptations for a specific trigger."""
        adaptations = []
        
        # Determine which strategies should respond to this trigger
        strategy_mapping = {
            AdaptationTrigger.PERFORMANCE_DECLINE: ["learning_rate", "exploration"],
            AdaptationTrigger.DETECTION_RISK: ["evasion", "exploration"],
            AdaptationTrigger.EXPLORATION_NEED: ["exploration"],
            AdaptationTrigger.PERSONA_MISMATCH: ["persona"],
            AdaptationTrigger.CONSISTENCY_DRIFT: ["consistency", "evasion"]
        }
        
        relevant_strategies = strategy_mapping.get(trigger, [])
        
        for strategy_name in relevant_strategies:
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]

                # Apply adaptation
                adaptation_result = strategy.apply_adaptation(agent, trigger)

                # Ensure strategy name is included in result
                if 'strategy' not in adaptation_result:
                    adaptation_result['strategy'] = strategy_name

                adaptations.append(adaptation_result)

                # Update trigger count
                self.metrics.adaptation_triggers[trigger] += 1
        
        return adaptations

    def _update_adaptation_effectiveness(self, current_metrics: Dict[str, Any]) -> None:
        """Update adaptation effectiveness based on ACTUAL performance changes."""
        if len(self.adaptation_history) < 2:
            return

        # FIXED: Calculate effectiveness from REAL performance improvements
        recent_adaptations = self.adaptation_history[-10:]  # Last 10 adaptation events
        effectiveness_scores = []

        for i, adaptation_event in enumerate(recent_adaptations):
            if i == 0:
                continue  # Skip first event (no baseline)

            # Get performance before and after adaptation
            prev_event = recent_adaptations[i-1]
            current_event = adaptation_event

            # Extract actual performance metrics - FIXED: Use correct key names
            prev_performance = prev_event.get('pre_adaptation_metrics', {})
            current_performance = current_event.get('post_adaptation_metrics', current_metrics)

            # Calculate real improvements
            improvements = 0
            total_metrics = 0

            # Check win rate improvement
            if 'win_rate' in prev_performance and 'win_rate' in current_performance:
                win_rate_improvement = current_performance['win_rate'] - prev_performance['win_rate']
                if win_rate_improvement > 0:
                    improvements += 1
                total_metrics += 1

            # Check detection risk reduction
            if 'detection_risk' in prev_performance and 'detection_risk' in current_performance:
                risk_reduction = prev_performance['detection_risk'] - current_performance['detection_risk']
                if risk_reduction > 0:
                    improvements += 1
                total_metrics += 1

            # Check learning efficiency improvement
            if 'learning_efficiency' in prev_performance and 'learning_efficiency' in current_performance:
                efficiency_improvement = current_performance['learning_efficiency'] - prev_performance['learning_efficiency']
                if efficiency_improvement > 0:
                    improvements += 1
                total_metrics += 1

            # Calculate effectiveness score for this adaptation
            if total_metrics > 0:
                effectiveness_score = improvements / total_metrics
                effectiveness_scores.append(effectiveness_score)

        # Update overall effectiveness based on real data
        if effectiveness_scores:
            calculated_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores)
            self.metrics.adaptation_effectiveness = calculated_effectiveness

            # ADDED: Comprehensive logging for verification
            if hasattr(self, 'debug_logging') and self.debug_logging:
                print(f"    🔍 Adaptation Effectiveness Calculation:")
                print(f"       Effectiveness Scores: {effectiveness_scores}")
                print(f"       Calculated Average: {calculated_effectiveness:.4f}")
                print(f"       Based on {len(effectiveness_scores)} adaptation events")
        else:
            # No real improvement data = no effectiveness
            self.metrics.adaptation_effectiveness = 0.0
            if hasattr(self, 'debug_logging') and self.debug_logging:
                print(f"    🔍 Adaptation Effectiveness: 0.0 (no improvement data)")

        # Log detailed calculation breakdown
        if hasattr(self, 'debug_logging') and self.debug_logging and len(self.adaptation_history) > 0:
            recent_adaptation = self.adaptation_history[-1]
            print(f"    🔍 Recent Adaptation Analysis:")
            print(f"       Training Step: {recent_adaptation.get('training_step', 'N/A')}")
            print(f"       Triggers: {recent_adaptation.get('triggers', [])}")
            print(f"       Adaptations Applied: {len(recent_adaptation.get('adaptations', []))}")

            # Show performance before/after if available
            pre_metrics = recent_adaptation.get('pre_adaptation_metrics', {})
            post_metrics = current_metrics
            if 'win_rate' in pre_metrics and 'win_rate' in post_metrics:
                win_rate_change = post_metrics['win_rate'] - pre_metrics['win_rate']
                print(f"       Win Rate Change: {pre_metrics['win_rate']:.3f} → {post_metrics['win_rate']:.3f} ({win_rate_change:+.3f})")
            if 'detection_risk' in pre_metrics and 'detection_risk' in post_metrics:
                risk_change = post_metrics['detection_risk'] - pre_metrics['detection_risk']
                print(f"       Detection Risk Change: {pre_metrics['detection_risk']:.3f} → {post_metrics['detection_risk']:.3f} ({risk_change:+.3f})")

        # Get the most recent adaptation and the one before it
        recent_adaptation = self.adaptation_history[-1]

        # Look for performance improvements after adaptation
        current_performance = current_metrics.get('win_rate', 0.0)
        pre_adaptation_performance = recent_adaptation['pre_adaptation_metrics'].get('win_rate', 0.0)

        # Calculate effectiveness as performance improvement
        performance_improvement = current_performance - pre_adaptation_performance

        # FIXED: Calculate effectiveness based on ACTUAL improvement data
        # Use dynamic scaling based on historical performance variance
        historical_performance = current_metrics.get('performance_history', [])
        if len(historical_performance) > 10:
            # Calculate actual performance variance to determine scaling
            performance_variance = np.var(historical_performance[-50:])  # Last 50 episodes
            # Use variance to determine what constitutes "significant" improvement
            significance_threshold = max(0.05, performance_variance * 2.0)  # Adaptive threshold
        else:
            significance_threshold = 0.1  # Default for insufficient data

        if performance_improvement > significance_threshold:
            # Significant positive improvement
            improvement_ratio = performance_improvement / significance_threshold
            effectiveness = 0.5 + min(0.5, improvement_ratio * 0.25)  # Scale 0.5-1.0
        elif performance_improvement < -significance_threshold:
            # Significant negative impact
            decline_ratio = abs(performance_improvement) / significance_threshold
            effectiveness = 0.5 - min(0.5, decline_ratio * 0.25)  # Scale 0.0-0.5
        else:
            # Marginal or no change - calculate based on adaptation context
            # Check if this was a corrective adaptation (high risk scenario)
            detection_risk = current_metrics.get('detection_risk', 0.0)
            if detection_risk > 0.7:
                # High risk scenario - maintaining performance is valuable
                effectiveness = 0.4 + (current_performance * 0.2)  # 0.4-0.6 range
            else:
                # Normal scenario - marginal effectiveness for no improvement
                effectiveness = 0.2 + (current_performance * 0.2)  # 0.2-0.4 range

        # Ensure effectiveness is in valid range
        effectiveness = max(0.0, min(1.0, effectiveness))

        # Update effectiveness for each strategy that was involved
        for adaptation in recent_adaptation['adaptations']:
            strategy_name = adaptation.get('strategy', 'unknown')
            if strategy_name in self.strategies:
                strategy = self.strategies[strategy_name]
                strategy.effectiveness_history.append(effectiveness)

                # Keep effectiveness history manageable
                if len(strategy.effectiveness_history) > 20:
                    strategy.effectiveness_history.pop(0)

        # Update overall adaptation effectiveness
        if hasattr(self.metrics, 'adaptation_effectiveness'):
            # Running average of effectiveness
            if self.metrics.adaptation_effectiveness == 0.0:
                self.metrics.adaptation_effectiveness = effectiveness
            else:
                # Weighted average (70% old, 30% new)
                self.metrics.adaptation_effectiveness = (
                    0.7 * self.metrics.adaptation_effectiveness + 0.3 * effectiveness
                )

    def get_adaptation_metrics(self) -> AdaptationMetrics:
        """Get current adaptation metrics."""
        return self.metrics
    
    def get_adaptation_history(self) -> List[Dict[str, Any]]:
        """Get adaptation history."""
        return self.adaptation_history.copy()
    
    def get_strategy_effectiveness(self) -> Dict[str, float]:
        """Get effectiveness scores for each adaptation strategy."""
        return {
            name: strategy.get_effectiveness_score()
            for name, strategy in self.strategies.items()
        }
    
    def reset_metrics(self) -> None:
        """Reset adaptation metrics."""
        self.metrics = AdaptationMetrics()
        self.adaptation_history = []
        
        # Reset strategy counters
        for strategy in self.strategies.values():
            strategy.activation_count = 0
            strategy.last_activation = 0
            strategy.effectiveness_history = []
