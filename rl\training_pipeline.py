"""
Training Pipeline for BlackJack Bot ML.

This module implements a comprehensive training pipeline that integrates
RL learning, evasion strategies, adaptive learning, and persona systems.
"""

import time
import json
import pickle
import math
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum

from .evasive_dqn_agent import EvasiveD<PERSON>NAgent
from .dqn_agent import DQNConfig
from .evasion_strategies import EvasionConfig
from .adaptive_learning import AdaptationConfig
from .rl_environment import BlackjackRLEnvironment
from .bankroll_manager import BankrollManager, BankrollConfig, BettingStrategy
from .advanced_money_management import AdvancedMoneyManager, AdvancedMoneyConfig, BettingProgression
from personas.persona_switcher import PersonaSwitcher, SwitchConfig
from core.game_logic import GameState, GameAction
from core.game_rules import BlackjackRules, get_training_rules
# Note: BlackjackSimulator not implemented yet - using environment for evaluation


class TrainingPhase(Enum):
    """Training phases."""
    INITIALIZATION = "initialization"
    EXPLORATION = "exploration"
    LEARNING = "learning"
    OPTIMIZATION = "optimization"
    EVALUATION = "evaluation"
    COMPLETED = "completed"


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""
    
    # Training parameters
    total_episodes: int = 10000
    episodes_per_phase: Dict[TrainingPhase, int] = field(default_factory=lambda: {
        TrainingPhase.EXPLORATION: 2000,
        TrainingPhase.LEARNING: 5000,
        TrainingPhase.OPTIMIZATION: 2000,
        TrainingPhase.EVALUATION: 1000
    })
    
    # Evaluation parameters
    evaluation_frequency: int = 500
    evaluation_episodes: int = 100
    
    # Checkpointing
    checkpoint_frequency: int = 1000
    save_directory: str = "training_checkpoints"
    
    # Performance thresholds
    target_win_rate: float = 0.45  # Target win rate
    target_consistency: float = 0.8  # Target consistency
    max_detection_risk: float = 0.3  # Maximum acceptable detection risk

    # Bankroll management
    bankroll_enabled: bool = True  # Enable bankroll-based training
    target_bankroll_growth: float = 0.2  # Target 20% bankroll growth
    bankroll_optimization_weight: float = 0.7  # Weight for bankroll vs win rate (0.7 = 70% bankroll, 30% win rate)

    # Advanced money management
    advanced_money_management: bool = True  # Enable sophisticated money management
    stress_test_enabled: bool = True  # Include stress test episodes
    loss_streak_training: bool = True  # Train specifically on loss recovery
    money_management_weight: float = 0.8  # Weight for money management vs basic strategy (0.8 = 80% money, 20% strategy)
    
    # Early stopping
    early_stopping_enabled: bool = True
    patience: int = 2000  # Episodes without improvement
    min_improvement: float = 0.01
    
    # Logging
    log_frequency: int = 100
    detailed_logging: bool = True


@dataclass
class TrainingMetrics:
    """Metrics for tracking training progress."""
    
    # Episode tracking
    current_episode: int = 0
    current_phase: TrainingPhase = TrainingPhase.INITIALIZATION
    
    # Performance metrics
    win_rate_history: List[float] = field(default_factory=list)
    avg_reward_history: List[float] = field(default_factory=list)
    consistency_history: List[float] = field(default_factory=list)
    detection_risk_history: List[float] = field(default_factory=list)

    # Bankroll metrics
    bankroll_history: List[float] = field(default_factory=list)
    roi_history: List[float] = field(default_factory=list)
    bet_size_history: List[float] = field(default_factory=list)
    bankroll_growth_rate: float = 0.0

    # Advanced money management metrics
    streak_performance: Dict[str, float] = field(default_factory=dict)
    loss_recovery_rate: float = 0.0
    max_drawdown_episodes: List[int] = field(default_factory=list)
    money_management_score: float = 0.0
    stress_test_survival_rate: float = 0.0
    
    # Training efficiency
    episodes_per_second: float = 0.0
    total_training_time: float = 0.0
    
    # Best performance
    best_win_rate: float = 0.0
    best_episode: int = 0
    episodes_since_improvement: int = 0
    
    # Phase completion
    completed_phases: List[TrainingPhase] = field(default_factory=list)


class TrainingPipeline:
    """
    Comprehensive training pipeline for BlackJack Bot ML.
    
    Integrates RL learning, evasion strategies, adaptive learning,
    and persona systems into a cohesive training process.
    """
    
    def __init__(self,
                 dqn_config: DQNConfig,
                 evasion_config: EvasionConfig,
                 adaptation_config: AdaptationConfig,
                 training_config: TrainingConfig,
                 persona_switcher: Optional[PersonaSwitcher] = None,
                 game_rules: Optional[BlackjackRules] = None,
                 bankroll_config: Optional[BankrollConfig] = None,
                 advanced_money_config: Optional[AdvancedMoneyConfig] = None):
        """
        Initialize training pipeline.

        Args:
            dqn_config: DQN agent configuration
            evasion_config: Evasion strategies configuration
            adaptation_config: Adaptive learning configuration
            training_config: Training pipeline configuration
            persona_switcher: Optional persona switcher
            game_rules: Game rules configuration (default: training-optimized rules)
        """
        self.dqn_config = dqn_config
        self.evasion_config = evasion_config
        self.adaptation_config = adaptation_config
        self.training_config = training_config
        self.persona_switcher = persona_switcher
        self.game_rules = game_rules if game_rules is not None else get_training_rules()
        self.bankroll_config = bankroll_config if bankroll_config is not None else BankrollConfig()
        self.advanced_money_config = advanced_money_config if advanced_money_config is not None else AdvancedMoneyConfig()

        # Initialize components
        self.agent = None
        self.environment = None
        self.simulator = None
        self.bankroll_manager = None
        self.advanced_money_manager = None
        
        # Training state
        self.metrics = TrainingMetrics()
        self.training_log = []
        self.is_training = False
        
        # Callbacks
        self.episode_callbacks: List[Callable] = []
        self.phase_callbacks: List[Callable] = []
        
        # Setup directories
        self.save_dir = Path(training_config.save_directory)
        self.save_dir.mkdir(exist_ok=True)
    
    def initialize_training(self) -> None:
        """Initialize training components."""
        print("Initializing training pipeline...")
        
        # Create agent
        self.agent = EvasiveDQNAgent(
            name="BlackJack RL Agent",
            dqn_config=self.dqn_config,
            evasion_config=self.evasion_config,
            adaptation_config=self.adaptation_config,
            persona_switcher=self.persona_switcher
        )
        
        # Create environment with configured rules
        self.environment = BlackjackRLEnvironment(rules=self.game_rules)

        # Initialize bankroll manager if enabled
        if self.training_config.bankroll_enabled:
            self.bankroll_manager = BankrollManager(self.bankroll_config)
            self.bankroll_manager.start_session()

        # Initialize advanced money manager if enabled
        if self.training_config.advanced_money_management:
            self.advanced_money_manager = AdvancedMoneyManager(self.advanced_money_config)
            print(f"   Advanced Money Management: {self.advanced_money_config.progression_strategy.value}")

        # Note: Using environment for evaluation (simulator not implemented yet)
        self.simulator = None

        # Set initial phase
        self.metrics.current_phase = TrainingPhase.EXPLORATION
        
        print(f"✅ Training initialized - Agent: {self.agent.name}")
        print(f"   Device: {self.agent.device}")
        print(f"   Network: {self.agent.get_network_info()['total_parameters']} parameters")
    
    def train(self) -> TrainingMetrics:
        """
        Execute complete training pipeline.
        
        Returns:
            Final training metrics
        """
        if not self.agent:
            self.initialize_training()
        
        print(f"Starting training for {self.training_config.total_episodes} episodes...")
        self.is_training = True
        start_time = time.time()
        
        try:
            # Training loop
            for episode in range(self.training_config.total_episodes):
                self.metrics.current_episode = episode
                
                # Update training phase
                self._update_training_phase()
                
                # Determine episode type and run episode
                if (self.training_config.stress_test_enabled and
                    self.training_config.advanced_money_management and
                    episode % 10 == 0):  # Every 10th episode is a stress test
                    episode_metrics = self._run_stress_test_episode()
                else:
                    episode_metrics = self._run_episode()
                
                # Update metrics
                self._update_metrics(episode_metrics)
                
                # Execute callbacks
                self._execute_episode_callbacks(episode_metrics)
                
                # Periodic evaluation
                if episode % self.training_config.evaluation_frequency == 0:
                    self._evaluate_agent()
                
                # Checkpointing
                if episode % self.training_config.checkpoint_frequency == 0:
                    self._save_checkpoint()
                
                # Logging
                if episode % self.training_config.log_frequency == 0:
                    self._log_progress()
                
                # Early stopping check
                if self._check_early_stopping():
                    print(f"Early stopping triggered at episode {episode}")
                    break
        
        except KeyboardInterrupt:
            print("Training interrupted by user")
        
        finally:
            self.is_training = False
            self.metrics.total_training_time = time.time() - start_time
            
            # Final evaluation
            print("Performing final evaluation...")
            final_evaluation = self._evaluate_agent()
            
            # Save final model
            self._save_final_model()
            
            print(f"Training completed in {self.metrics.total_training_time:.2f} seconds")
            print(f"Final win rate: {final_evaluation['win_rate']:.3f}")
            print(f"Final detection risk: {final_evaluation['detection_risk']:.3f}")
        
        return self.metrics
    
    def _run_episode(self) -> Dict[str, Any]:
        """Run a single training episode with bankroll management."""
        import time

        episode_start_time = time.time()

        # Reset environment
        state = self.environment.reset()
        total_reward = 0.0
        bankroll_reward = 0.0  # Separate tracking for bankroll-based rewards
        steps = 0
        episode_actions = []
        episode_bets = []
        episode_outcomes = []
        money_result = {}  # Store money management result for metrics
        max_steps = 50  # Safety limit to prevent infinite loops

        # Calculate initial bet size based on management type
        if self.training_config.advanced_money_management:
            initial_bet_size = self._calculate_advanced_bet_size()
        else:
            initial_bet_size = self._calculate_episode_bet_size()

        while not self.environment.done and steps < max_steps:
            # Get action from agent (use game_state from environment)
            action = self.agent.get_action(self.environment.game_state)
            episode_actions.append(action)

            # Take step in environment
            next_state, reward, done, info = self.environment.step(action)

            # Calculate reward based on management type
            if self.training_config.advanced_money_management and self.advanced_money_manager and steps == 0:
                # Advanced money management
                hand_outcome = self._determine_hand_outcome(reward.total, info)
                episode_outcomes.append(hand_outcome)

                # Update advanced money manager
                won = reward.total > 0
                money_result = self.advanced_money_manager.update_result(
                    bet_amount=initial_bet_size,
                    won=won,
                    payout_multiplier=hand_outcome['payout_multiplier']
                )

                # Calculate money management optimized reward
                bankroll_reward = self._calculate_money_management_reward(
                    original_reward=reward.total,
                    money_result=money_result,
                    bet_size=initial_bet_size
                )

                episode_bets.append(initial_bet_size)
            elif self.bankroll_manager and steps == 0:  # Regular bankroll management
                # Determine hand outcome for bankroll management
                hand_outcome = self._determine_hand_outcome(reward.total, info)
                episode_outcomes.append(hand_outcome)

                # Update bankroll based on outcome
                bankroll_update = self.bankroll_manager.update_bankroll(
                    bet_amount=initial_bet_size,
                    result=hand_outcome['result'],
                    payout_multiplier=hand_outcome['payout_multiplier']
                )

                # Calculate bankroll-optimized reward
                bankroll_reward = self._calculate_bankroll_reward(
                    original_reward=reward.total,
                    bankroll_update=bankroll_update,
                    bet_size=initial_bet_size
                )

                episode_bets.append(initial_bet_size)
            else:
                # Use original reward if no bankroll management
                bankroll_reward = reward.total
                if steps == 0:
                    if self.training_config.advanced_money_management:
                        episode_bets.append(self.advanced_money_config.base_bet_size)
                    else:
                        episode_bets.append(self.bankroll_config.base_bet_size)

            # Update agent with bankroll-optimized reward
            game_state = info.get('game_state', self.environment.game_state)
            final_reward = bankroll_reward if self.training_config.bankroll_enabled else reward.total
            self.agent.update_experience(final_reward, game_state, done)

            # Update for next iteration
            state = next_state
            total_reward += reward.total
            steps += 1

        episode_duration = time.time() - episode_start_time

        # Check for timeout warning
        if episode_duration > 10:  # 10 second warning for training episodes
            print(f"      Warning: Training episode took {episode_duration:.1f}s")

        # Calculate episode metrics
        episode_metrics = {
            "episode": self.metrics.current_episode,
            "total_reward": total_reward,
            "bankroll_reward": bankroll_reward,
            "bet_size": episode_bets[0] if episode_bets else self.bankroll_config.base_bet_size,
            "steps": steps,
            "actions": episode_actions,
            "outcomes": episode_outcomes,
            "win": total_reward > 0,
            "detection_risk": self.agent.evasion_manager.current_risk_level,
            "consistency": self.agent.consistency_scores[-1] if self.agent.consistency_scores else 0.0,
            "duration": episode_duration
        }

        # Add money management metrics if available
        if self.training_config.advanced_money_management and self.advanced_money_manager:
            money_report = self.advanced_money_manager.get_comprehensive_report()
            episode_metrics.update({
                "current_bankroll": money_report["current_state"]["bankroll"],
                "bankroll_roi": money_report["current_state"]["roi"],
                "money_management_roi": money_report["current_state"]["roi"],
                "current_streak": money_report["streak_analysis"]["current_streak"],
                "max_drawdown": money_report["risk_metrics"]["max_drawdown"],
                "emergency_action": money_result.get("emergency_action")
            })
        elif self.bankroll_manager:
            bankroll_summary = self.bankroll_manager.get_bankroll_summary()
            episode_metrics.update({
                "current_bankroll": bankroll_summary["current_bankroll"],
                "bankroll_roi": bankroll_summary["roi"],
                "session_profit": bankroll_summary["session_profit"],
                "risk_of_ruin": bankroll_summary["risk_of_ruin"]
            })

        return episode_metrics

    def _run_stress_test_episode(self) -> Dict[str, Any]:
        """Run a stress test episode with simulated adverse conditions."""
        import time
        import random

        episode_start_time = time.time()

        # Simulate adverse conditions (forced losses for stress testing)
        stress_factor = random.choice([0.7, 0.8, 0.9])  # Reduce win probability

        # Reset environment
        state = self.environment.reset()
        total_reward = 0.0
        bankroll_reward = 0.0
        steps = 0
        episode_actions = []
        episode_bets = []
        episode_outcomes = []
        max_steps = 50

        # Calculate bet size with advanced money management
        initial_bet_size = self._calculate_advanced_bet_size()

        while not self.environment.done and steps < max_steps:
            # Get action from agent
            action = self.agent.get_action(self.environment.game_state)
            episode_actions.append(action)

            # Take step in environment
            next_state, reward, done, info = self.environment.step(action)

            # Apply stress factor (simulate bad luck)
            if reward.total > 0 and random.random() > stress_factor:
                # Force some wins to become losses for stress testing
                reward.total = -abs(reward.total)

            # Update advanced money management
            if self.advanced_money_manager and steps == 0:
                hand_outcome = self._determine_hand_outcome(reward.total, info)
                episode_outcomes.append(hand_outcome)

                # Update advanced money manager
                won = reward.total > 0
                money_result = self.advanced_money_manager.update_result(
                    bet_amount=initial_bet_size,
                    won=won,
                    payout_multiplier=hand_outcome['payout_multiplier']
                )

                # Calculate money management optimized reward
                bankroll_reward = self._calculate_money_management_reward(
                    original_reward=reward.total,
                    money_result=money_result,
                    bet_size=initial_bet_size
                )

                episode_bets.append(initial_bet_size)
            else:
                bankroll_reward = reward.total
                if steps == 0:
                    episode_bets.append(self.advanced_money_config.base_bet_size)

            # Update agent with money management optimized reward
            game_state = info.get('game_state', self.environment.game_state)
            final_reward = bankroll_reward if self.training_config.advanced_money_management else reward.total
            self.agent.update_experience(final_reward, game_state, done)

            # Update for next iteration
            state = next_state
            total_reward += reward.total
            steps += 1

        episode_duration = time.time() - episode_start_time

        # Calculate episode metrics
        episode_metrics = {
            "episode": self.metrics.current_episode,
            "episode_type": "stress_test",
            "stress_factor": stress_factor,
            "total_reward": total_reward,
            "money_management_reward": bankroll_reward,
            "bet_size": episode_bets[0] if episode_bets else self.advanced_money_config.base_bet_size,
            "steps": steps,
            "actions": episode_actions,
            "outcomes": episode_outcomes,
            "win": total_reward > 0,
            "detection_risk": self.agent.evasion_manager.current_risk_level,
            "consistency": self.agent.consistency_scores[-1] if self.agent.consistency_scores else 0.0,
            "duration": episode_duration
        }

        # Add advanced money management metrics if available
        if self.advanced_money_manager:
            money_report = self.advanced_money_manager.get_comprehensive_report()
            episode_metrics.update({
                "current_bankroll": money_report["current_state"]["bankroll"],
                "bankroll_roi": money_report["current_state"]["roi"],  # Fixed: use bankroll_roi for consistency
                "money_management_roi": money_report["current_state"]["roi"],
                "current_streak": money_report["streak_analysis"]["current_streak"],
                "max_drawdown": money_report["risk_metrics"]["max_drawdown"],
                "emergency_action": money_result.get("emergency_action")
            })

        return episode_metrics

    def _calculate_episode_bet_size(self) -> float:
        """Calculate bet size for the episode using ML model confidence."""
        if not self.bankroll_manager:
            return self.bankroll_config.base_bet_size

        # Get model confidence from agent's recent performance
        model_confidence = self._estimate_model_confidence()

        # Estimate edge based on recent win rate vs expected
        estimated_edge = self._estimate_current_edge()

        # Calculate optimal bet size
        bet_size = self.bankroll_manager.calculate_bet_size(
            model_confidence=model_confidence,
            estimated_edge=estimated_edge
        )

        return bet_size

    def _calculate_advanced_bet_size(self) -> float:
        """Calculate bet size using advanced money management."""
        if not self.advanced_money_manager:
            return self.advanced_money_config.base_bet_size

        # Get model confidence and edge estimates
        model_confidence = self._estimate_model_confidence()
        estimated_edge = self._estimate_current_edge()

        # Get game context
        game_context = {
            "episode": self.metrics.current_episode,
            "recent_performance": self._get_recent_performance_context()
        }

        # Calculate optimal bet size with advanced money management
        bet_size = self.advanced_money_manager.calculate_bet_size(
            model_confidence=model_confidence,
            estimated_edge=estimated_edge,
            game_context=game_context
        )

        return bet_size

    def _get_recent_performance_context(self) -> Dict[str, Any]:
        """Get recent performance context for money management."""
        if len(self.training_log) < 10:
            return {"insufficient_data": True}

        recent_episodes = self.training_log[-20:]  # Last 20 episodes

        # Calculate recent metrics
        recent_wins = sum(1 for ep in recent_episodes if ep.get('win', False))
        recent_losses = len(recent_episodes) - recent_wins
        win_rate = recent_wins / len(recent_episodes)

        # Detect current streak
        current_streak_length = 0
        current_streak_type = None

        for ep in reversed(recent_episodes):
            if ep.get('win', False):
                if current_streak_type == 'win' or current_streak_type is None:
                    current_streak_length += 1
                    current_streak_type = 'win'
                else:
                    break
            else:
                if current_streak_type == 'loss' or current_streak_type is None:
                    current_streak_length += 1
                    current_streak_type = 'loss'
                else:
                    break

        return {
            "recent_win_rate": win_rate,
            "recent_wins": recent_wins,
            "recent_losses": recent_losses,
            "current_streak_type": current_streak_type,
            "current_streak_length": current_streak_length,
            "volatility": self._calculate_recent_volatility(recent_episodes)
        }

    def _calculate_recent_volatility(self, recent_episodes: List[Dict]) -> float:
        """Calculate recent performance volatility."""
        if len(recent_episodes) < 5:
            return 0.5  # Default moderate volatility

        rewards = [ep.get('total_reward', 0) for ep in recent_episodes]
        if not rewards:
            return 0.5

        mean_reward = sum(rewards) / len(rewards)
        variance = sum((r - mean_reward) ** 2 for r in rewards) / len(rewards)
        volatility = math.sqrt(variance)

        # Normalize to 0-1 scale
        return min(1.0, volatility / 2.0)

    def _calculate_money_management_reward(self, original_reward: float,
                                         money_result: Dict[str, Any],
                                         bet_size: float) -> float:
        """Calculate reward optimized for money management."""
        if not self.training_config.advanced_money_management:
            return original_reward

        # Get money management weight
        money_weight = self.training_config.money_management_weight
        strategy_weight = 1.0 - money_weight

        # Calculate money management component
        money_roi = money_result.get('roi', 0.0)
        bankroll_growth = money_result.get('current_bankroll', self.advanced_money_config.initial_bankroll) / self.advanced_money_config.initial_bankroll - 1.0

        # Money management reward focuses on long-term bankroll growth
        money_component = bankroll_growth * 10.0  # Scale for reward magnitude

        # Add streak management bonus
        current_streak = money_result.get('current_streak')
        if current_streak:
            if current_streak.type.value == 'loss_streak' and current_streak.length <= 3:
                # Reward good loss management
                money_component += 0.5
            elif current_streak.type.value == 'win_streak' and current_streak.length >= 3:
                # Reward win streak capitalization
                money_component += 0.3

        # Add emergency action penalty
        if money_result.get('emergency_action'):
            money_component -= 2.0  # Heavy penalty for emergency conditions

        # Strategy component (original reward)
        strategy_component = original_reward

        # Combine components
        final_reward = (money_weight * money_component +
                       strategy_weight * strategy_component)

        # Add bet sizing efficiency bonus
        if original_reward > 0:
            # Reward larger bets when winning (if justified by confidence)
            bet_efficiency = min(bet_size / self.advanced_money_config.base_bet_size, 3.0)
            final_reward += bet_efficiency * 0.1

        return final_reward

    def _estimate_model_confidence(self) -> float:
        """Estimate ML model confidence based on recent performance consistency."""
        if len(self.training_log) < 10:
            return 0.5  # Default confidence

        # Analyze recent performance consistency
        recent_episodes = self.training_log[-20:]  # Last 20 episodes
        recent_wins = [ep.get('win', False) for ep in recent_episodes]
        recent_consistency = [ep.get('consistency', 0.0) for ep in recent_episodes]

        # Calculate confidence based on consistency and performance stability
        avg_consistency = sum(recent_consistency) / len(recent_consistency) if recent_consistency else 0.5
        win_rate = sum(recent_wins) / len(recent_wins) if recent_wins else 0.5

        # Confidence increases with both high consistency and reasonable win rate
        confidence = (avg_consistency * 0.6) + (min(win_rate, 0.8) * 0.4)

        return max(0.1, min(0.9, confidence))

    def _estimate_current_edge(self) -> float:
        """Estimate current edge based on recent performance vs basic strategy."""
        if len(self.training_log) < 20:
            return 0.0  # No edge estimate with insufficient data

        recent_episodes = self.training_log[-50:]  # Last 50 episodes
        recent_rewards = [ep.get('total_reward', 0) for ep in recent_episodes]

        # Calculate average reward per hand
        avg_reward = sum(recent_rewards) / len(recent_rewards) if recent_rewards else 0.0

        # Basic strategy expected value is approximately -0.005 (0.5% house edge)
        basic_strategy_ev = -0.005

        # Estimated edge is improvement over basic strategy
        estimated_edge = avg_reward - basic_strategy_ev

        # Clamp edge to reasonable range
        return max(-0.1, min(0.05, estimated_edge))

    def _determine_hand_outcome(self, reward: float, info: Dict[str, Any]) -> Dict[str, Any]:
        """Determine hand outcome for bankroll management."""
        # Analyze reward to determine outcome type
        if reward > 1.4:  # Blackjack (1.5x payout)
            return {"result": "blackjack", "payout_multiplier": 1.5}
        elif reward > 0:  # Regular win
            return {"result": "win", "payout_multiplier": 1.0}
        elif reward == 0:  # Push
            return {"result": "push", "payout_multiplier": 0.0}
        else:  # Loss
            return {"result": "loss", "payout_multiplier": 0.0}

    def _calculate_bankroll_reward(self, original_reward: float,
                                 bankroll_update: Dict[str, Any],
                                 bet_size: float) -> float:
        """Calculate bankroll-optimized reward."""
        if not self.training_config.bankroll_enabled:
            return original_reward

        # Get bankroll optimization weight
        bankroll_weight = self.training_config.bankroll_optimization_weight
        win_rate_weight = 1.0 - bankroll_weight

        # Calculate bankroll component (ROI improvement)
        bankroll_roi = bankroll_update.get('roi', 0.0)
        bankroll_component = bankroll_roi * 10.0  # Scale ROI for reward magnitude

        # Calculate win rate component (normalized original reward)
        win_rate_component = original_reward

        # Combine components with weights
        bankroll_reward = (bankroll_weight * bankroll_component +
                          win_rate_weight * win_rate_component)

        # Add bet size efficiency bonus (reward larger bets when confident)
        if original_reward > 0 and bet_size > self.bankroll_config.base_bet_size:
            bet_efficiency_bonus = (bet_size / self.bankroll_config.base_bet_size - 1.0) * 0.1
            bankroll_reward += bet_efficiency_bonus

        return bankroll_reward

    def _update_training_phase(self) -> None:
        """Update current training phase based on episode count."""
        episode = self.metrics.current_episode
        
        # Determine current phase
        if episode < self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION]:
            new_phase = TrainingPhase.EXPLORATION
        elif episode < (self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION] + 
                       self.training_config.episodes_per_phase[TrainingPhase.LEARNING]):
            new_phase = TrainingPhase.LEARNING
        elif episode < (self.training_config.episodes_per_phase[TrainingPhase.EXPLORATION] + 
                       self.training_config.episodes_per_phase[TrainingPhase.LEARNING] +
                       self.training_config.episodes_per_phase[TrainingPhase.OPTIMIZATION]):
            new_phase = TrainingPhase.OPTIMIZATION
        else:
            new_phase = TrainingPhase.EVALUATION
        
        # Check for phase transition
        if new_phase != self.metrics.current_phase:
            old_phase = self.metrics.current_phase
            self.metrics.current_phase = new_phase
            
            if old_phase not in self.metrics.completed_phases:
                self.metrics.completed_phases.append(old_phase)
            
            print(f"Phase transition: {old_phase.value} → {new_phase.value}")
            self._execute_phase_callbacks(old_phase, new_phase)
            
            # Adjust agent parameters for new phase
            self._adjust_agent_for_phase(new_phase)
    
    def _adjust_agent_for_phase(self, phase: TrainingPhase) -> None:
        """Adjust agent parameters for training phase."""
        if phase == TrainingPhase.EXPLORATION:
            # High exploration
            self.agent.epsilon = 0.9
            self.agent.epsilon_decay = 0.995
        elif phase == TrainingPhase.LEARNING:
            # Balanced exploration/exploitation
            self.agent.epsilon = 0.5
            self.agent.epsilon_decay = 0.999
        elif phase == TrainingPhase.OPTIMIZATION:
            # Low exploration, focus on optimization
            self.agent.epsilon = 0.1
            self.agent.epsilon_decay = 0.9995
        elif phase == TrainingPhase.EVALUATION:
            # Minimal exploration
            self.agent.epsilon = 0.05
            self.agent.epsilon_decay = 1.0
    
    def _update_metrics(self, episode_metrics: Dict[str, Any]) -> None:
        """Update training metrics with episode results."""
        # Calculate running averages
        window_size = 100

        # Win rate
        recent_wins = [m.get("win", False) for m in self.training_log[-window_size:]]
        recent_wins.append(episode_metrics["win"])
        win_rate = sum(recent_wins) / len(recent_wins)
        self.metrics.win_rate_history.append(win_rate)

        # Average reward
        recent_rewards = [m.get("total_reward", 0) for m in self.training_log[-window_size:]]
        recent_rewards.append(episode_metrics["total_reward"])
        avg_reward = sum(recent_rewards) / len(recent_rewards)
        self.metrics.avg_reward_history.append(avg_reward)

        # Consistency
        self.metrics.consistency_history.append(episode_metrics["consistency"])

        # Detection risk
        self.metrics.detection_risk_history.append(episode_metrics["detection_risk"])

        # Bankroll metrics (if bankroll management is enabled)
        if self.training_config.bankroll_enabled and "current_bankroll" in episode_metrics:
            self.metrics.bankroll_history.append(episode_metrics["current_bankroll"])
            self.metrics.roi_history.append(episode_metrics["bankroll_roi"])
            self.metrics.bet_size_history.append(episode_metrics["bet_size"])

            # Calculate bankroll growth rate
            if len(self.metrics.bankroll_history) > 1:
                initial_bankroll = self.bankroll_config.initial_bankroll
                current_bankroll = episode_metrics["current_bankroll"]
                self.metrics.bankroll_growth_rate = (current_bankroll - initial_bankroll) / initial_bankroll

        # Check for best performance (consider both win rate and bankroll growth)
        performance_metric = self._calculate_combined_performance_metric(win_rate, episode_metrics)

        if performance_metric > self._get_best_performance_metric():
            self.metrics.best_win_rate = win_rate
            self.metrics.best_episode = self.metrics.current_episode
            self.metrics.episodes_since_improvement = 0
        else:
            self.metrics.episodes_since_improvement += 1

        # Calculate training speed
        if len(self.training_log) > 0:
            time_diff = time.time() - self.training_log[-1].get("timestamp", time.time())
            if time_diff > 0:
                self.metrics.episodes_per_second = 1.0 / time_diff

        # Add to training log
        episode_metrics["timestamp"] = time.time()
        episode_metrics["win_rate"] = win_rate
        episode_metrics["avg_reward"] = avg_reward
        episode_metrics["performance_metric"] = performance_metric
        self.training_log.append(episode_metrics)

        # Keep log manageable
        if len(self.training_log) > 10000:
            self.training_log = self.training_log[-5000:]

    def _calculate_combined_performance_metric(self, win_rate: float,
                                             episode_metrics: Dict[str, Any]) -> float:
        """Calculate combined performance metric considering both win rate and bankroll growth."""
        if not self.training_config.bankroll_enabled:
            return win_rate

        # Get weights
        bankroll_weight = self.training_config.bankroll_optimization_weight
        win_rate_weight = 1.0 - bankroll_weight

        # Normalize win rate (0.5 = break-even, 1.0 = perfect)
        normalized_win_rate = min(1.0, max(0.0, win_rate))

        # Get bankroll growth rate (normalized to 0-1 scale)
        bankroll_roi = episode_metrics.get("bankroll_roi", 0.0)
        # Normalize ROI: 0.2 (20% growth) = 1.0, -0.5 (50% loss) = 0.0
        normalized_bankroll = min(1.0, max(0.0, (bankroll_roi + 0.5) / 0.7))

        # Combine metrics
        combined_metric = (win_rate_weight * normalized_win_rate +
                          bankroll_weight * normalized_bankroll)

        return combined_metric

    def _get_best_performance_metric(self) -> float:
        """Get the best performance metric achieved so far."""
        if not self.training_log:
            return 0.0

        best_metrics = [ep.get("performance_metric", 0.0) for ep in self.training_log]
        return max(best_metrics) if best_metrics else 0.0

    def _evaluate_agent(self) -> Dict[str, Any]:
        """Evaluate agent performance."""
        import time

        eval_start_time = time.time()
        num_eval_episodes = self.training_config.evaluation_episodes

        print(f"Evaluating agent at episode {self.metrics.current_episode}...")
        print(f"   Running {num_eval_episodes} evaluation episodes...")

        # Run evaluation episodes
        evaluation_results = []

        # Store original epsilon and set to 0 for all evaluation
        old_epsilon = self.agent.epsilon
        self.agent.epsilon = 0.0  # No exploration during evaluation

        try:
            for eval_ep in range(num_eval_episodes):
                episode_start_time = time.time()

                state = self.environment.reset()
                total_reward = 0.0
                steps = 0
                max_steps = 50  # Safety limit to prevent infinite loops

                while not self.environment.done and steps < max_steps:
                    action = self.agent.get_action(self.environment.game_state)
                    next_state, reward, done, info = self.environment.step(action)

                    state = next_state
                    total_reward += reward.total
                    steps += 1

                episode_duration = time.time() - episode_start_time

                # Log progress for each evaluation episode
                win_status = "WIN" if total_reward > 0 else "LOSS"
                print(f"      Eval {eval_ep+1}/{num_eval_episodes}: {win_status} "
                      f"(Reward: {total_reward:.2f}, Steps: {steps}, "
                      f"Time: {episode_duration:.2f}s)")

                evaluation_results.append({
                    "reward": total_reward,
                    "win": total_reward > 0,
                    "steps": steps,
                    "duration": episode_duration
                })

                # Check for timeout (if single episode takes too long)
                if episode_duration > 30:  # 30 second timeout per episode
                    print(f"      Warning: Evaluation episode {eval_ep+1} took {episode_duration:.1f}s")

        finally:
            # Always restore epsilon
            self.agent.epsilon = old_epsilon
        
        # Calculate evaluation metrics
        win_rate = sum(1 for r in evaluation_results if r["win"]) / len(evaluation_results)
        avg_reward = sum(r["reward"] for r in evaluation_results) / len(evaluation_results)
        total_eval_time = time.time() - eval_start_time
        avg_episode_time = sum(r["duration"] for r in evaluation_results) / len(evaluation_results)

        # Get agent statistics
        agent_stats = self.agent.get_comprehensive_stats()

        evaluation = {
            "episode": self.metrics.current_episode,
            "win_rate": win_rate,
            "avg_reward": avg_reward,
            "detection_risk": agent_stats["detection_assessment"]["current_risk"],
            "consistency": agent_stats.get("current_consistency", 0.0),
            "evasion_effectiveness": agent_stats["evasion_metrics"]["evasion_effectiveness"],
            "total_adaptations": agent_stats["adaptive_learning_metrics"]["total_adaptations"],
            "eval_time": total_eval_time,
            "avg_episode_time": avg_episode_time
        }

        print(f"   📊 Evaluation Summary:")
        print(f"      Win rate: {win_rate:.3f}")
        print(f"      Avg reward: {avg_reward:.3f}")
        print(f"      Detection risk: {evaluation['detection_risk']:.3f}")
        print(f"      Total eval time: {total_eval_time:.2f}s")
        print(f"      Avg episode time: {avg_episode_time:.2f}s")

        return evaluation
    
    def _check_early_stopping(self) -> bool:
        """Check if early stopping criteria are met."""
        if not self.training_config.early_stopping_enabled:
            return False

        # Check if we've reached target performance (win rate + detection risk)
        win_rate_target_met = (len(self.metrics.win_rate_history) > 0 and
                              self.metrics.win_rate_history[-1] >= self.training_config.target_win_rate)

        detection_risk_target_met = (len(self.metrics.detection_risk_history) > 0 and
                                   self.metrics.detection_risk_history[-1] <= self.training_config.max_detection_risk)

        # Check bankroll growth target if enabled
        bankroll_target_met = True  # Default to True if bankroll not enabled
        if self.training_config.bankroll_enabled and len(self.metrics.roi_history) > 0:
            current_roi = self.metrics.roi_history[-1]
            bankroll_target_met = current_roi >= self.training_config.target_bankroll_growth

        # All targets must be met for early stopping
        if win_rate_target_met and detection_risk_target_met and bankroll_target_met:
            print("Target performance reached!")
            if self.training_config.bankroll_enabled:
                print(f"  Win Rate: {self.metrics.win_rate_history[-1]:.3f} >= {self.training_config.target_win_rate:.3f}")
                print(f"  Detection Risk: {self.metrics.detection_risk_history[-1]:.3f} <= {self.training_config.max_detection_risk:.3f}")
                print(f"  Bankroll ROI: {self.metrics.roi_history[-1]:.3f} >= {self.training_config.target_bankroll_growth:.3f}")
            return True

        # Check patience
        if self.metrics.episodes_since_improvement >= self.training_config.patience:
            print(f"No improvement for {self.training_config.patience} episodes")
            return True

        return False
    
    def _save_checkpoint(self) -> None:
        """Save training checkpoint."""
        checkpoint_path = self.save_dir / f"checkpoint_episode_{self.metrics.current_episode}.pkl"
        
        checkpoint_data = {
            "episode": self.metrics.current_episode,
            "agent_state": self.agent.state_dict(),
            "metrics": self.metrics,
            "training_log": self.training_log[-1000:],  # Last 1000 episodes
            "configs": {
                "dqn": self.dqn_config,
                "evasion": self.evasion_config,
                "adaptation": self.adaptation_config,
                "training": self.training_config
            }
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        print(f"Checkpoint saved: {checkpoint_path}")
    
    def _save_final_model(self) -> None:
        """Save final trained model."""
        model_path = self.save_dir / "final_model.pkl"
        
        final_data = {
            "agent_state": self.agent.state_dict(),
            "final_metrics": self.metrics,
            "training_log": self.training_log,
            "agent_stats": self.agent.get_comprehensive_stats(),
            "configs": {
                "dqn": self.dqn_config,
                "evasion": self.evasion_config,
                "adaptation": self.adaptation_config,
                "training": self.training_config
            }
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(final_data, f)
        
        print(f"Final model saved: {model_path}")
    
    def _log_progress(self) -> None:
        """Log training progress."""
        episode = self.metrics.current_episode
        phase = self.metrics.current_phase.value
        
        if len(self.metrics.win_rate_history) > 0:
            win_rate = self.metrics.win_rate_history[-1]
            avg_reward = self.metrics.avg_reward_history[-1]
            detection_risk = self.metrics.detection_risk_history[-1]
            consistency = self.metrics.consistency_history[-1]
            
            print(f"Episode {episode:5d} | Phase: {phase:12s} | "
                  f"Win Rate: {win_rate:.3f} | Reward: {avg_reward:6.2f} | "
                  f"Risk: {detection_risk:.3f} | Consistency: {consistency:.3f}")
    
    def add_episode_callback(self, callback: Callable) -> None:
        """Add callback to be executed after each episode."""
        self.episode_callbacks.append(callback)
    
    def add_phase_callback(self, callback: Callable) -> None:
        """Add callback to be executed on phase transitions."""
        self.phase_callbacks.append(callback)
    
    def _execute_episode_callbacks(self, episode_metrics: Dict[str, Any]) -> None:
        """Execute episode callbacks."""
        for callback in self.episode_callbacks:
            try:
                callback(self, episode_metrics)
            except Exception as e:
                print(f"Episode callback error: {e}")
    
    def _execute_phase_callbacks(self, old_phase: TrainingPhase, new_phase: TrainingPhase) -> None:
        """Execute phase transition callbacks."""
        for callback in self.phase_callbacks:
            try:
                callback(self, old_phase, new_phase)
            except Exception as e:
                print(f"Phase callback error: {e}")
    
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """Load training checkpoint."""
        with open(checkpoint_path, 'rb') as f:
            checkpoint_data = pickle.load(f)
        
        # Restore agent state
        if self.agent:
            self.agent.load_state_dict(checkpoint_data["agent_state"])
        
        # Restore metrics
        self.metrics = checkpoint_data["metrics"]
        self.training_log = checkpoint_data["training_log"]
        
        print(f"Checkpoint loaded from episode {self.metrics.current_episode}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get comprehensive training summary."""
        return {
            "training_config": self.training_config,
            "final_metrics": self.metrics,
            "agent_stats": self.agent.get_comprehensive_stats() if self.agent else None,
            "training_phases": [phase.value for phase in self.metrics.completed_phases],
            "performance_summary": {
                "best_win_rate": self.metrics.best_win_rate,
                "best_episode": self.metrics.best_episode,
                "final_win_rate": self.metrics.win_rate_history[-1] if self.metrics.win_rate_history else 0.0,
                "total_training_time": self.metrics.total_training_time,
                "episodes_per_second": self.metrics.episodes_per_second
            }
        }
