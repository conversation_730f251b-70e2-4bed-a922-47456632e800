"""
Interactive Game Actions Widget for BlackJack Bot ML GUI.

This module implements the complete interactive blackjack interface with
all game actions including insurance, surrender, splitting, and side bets.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any, Optional, Callable
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.card import Card
from core.game_logic import BlackjackGame, GameState, GameAction, GameResult
from core.game_rules import BlackjackRules, get_realistic_rules


class GameActionsWidget:
    """
    Interactive game actions widget for complete blackjack gameplay.
    
    Provides buttons and interfaces for all blackjack actions including:
    - Basic actions (Hit, Stand, Double Down)
    - Advanced actions (Split, Surrender, Insurance)
    - Side bets (Perfect Pairs, 21+3)
    - Bet management and payout calculations
    """
    
    def __init__(self, parent, on_action_taken: Optional[Callable] = None,
                 on_game_state_change: Optional[Callable] = None):
        """
        Initialize the game actions widget.
        
        Args:
            parent: Parent tkinter widget
            on_action_taken: Callback for when action is taken
            on_game_state_change: Callback for game state changes
        """
        self.parent = parent
        self.on_action_taken = on_action_taken
        self.on_game_state_change = on_game_state_change
        
        # Game state
        self.game = BlackjackGame(rules=get_realistic_rules())
        self.current_game_state = None
        self.bet_amount = 10.0
        self.insurance_bet = 0.0
        self.side_bets = {}
        self.bankroll = 1000.0
        
        # UI state
        self.action_buttons = {}
        self.bet_widgets = {}
        self.side_bet_widgets = {}
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create all action widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        
        # Title
        self.title_label = ttk.Label(
            self.main_frame,
            text="Game Actions",
            font=('Arial', 14, 'bold')
        )
        
        # Create sections
        self.create_betting_section()
        self.create_action_buttons_section()
        self.create_side_bets_section()
        self.create_game_info_section()
        
    def create_betting_section(self):
        """Create betting controls section."""
        self.betting_frame = ttk.LabelFrame(
            self.main_frame,
            text="Betting",
            padding="10"
        )
        
        # Main bet controls
        self.main_bet_frame = ttk.Frame(self.betting_frame)
        
        ttk.Label(self.main_bet_frame, text="Main Bet:").grid(row=0, column=0, sticky='w')
        
        self.bet_var = tk.DoubleVar(value=self.bet_amount)
        self.bet_spinbox = ttk.Spinbox(
            self.main_bet_frame,
            from_=1.0,
            to=100.0,
            increment=1.0,
            textvariable=self.bet_var,
            width=10,
            command=self.on_bet_change
        )
        self.bet_spinbox.grid(row=0, column=1, padx=(5, 0))
        
        # Insurance bet controls
        self.insurance_frame = ttk.Frame(self.betting_frame)
        
        ttk.Label(self.insurance_frame, text="Insurance:").grid(row=0, column=0, sticky='w')
        
        self.insurance_var = tk.DoubleVar(value=0.0)
        self.insurance_spinbox = ttk.Spinbox(
            self.insurance_frame,
            from_=0.0,
            to=50.0,
            increment=0.5,
            textvariable=self.insurance_var,
            width=10,
            state='disabled'
        )
        self.insurance_spinbox.grid(row=0, column=1, padx=(5, 0))
        
        # Bankroll display
        self.bankroll_frame = ttk.Frame(self.betting_frame)
        
        ttk.Label(self.bankroll_frame, text="Bankroll:").grid(row=0, column=0, sticky='w')
        
        self.bankroll_label = ttk.Label(
            self.bankroll_frame,
            text=f"${self.bankroll:.2f}",
            font=('Arial', 10, 'bold')
        )
        self.bankroll_label.grid(row=0, column=1, padx=(5, 0))
        
    def create_action_buttons_section(self):
        """Create main action buttons section."""
        self.actions_frame = ttk.LabelFrame(
            self.main_frame,
            text="Actions",
            padding="10"
        )
        
        # Basic action buttons
        self.basic_actions_frame = ttk.Frame(self.actions_frame)
        
        # Hit button
        self.hit_btn = ttk.Button(
            self.basic_actions_frame,
            text="Hit",
            command=lambda: self.take_action(GameAction.HIT),
            style='Action.TButton',
            width=12
        )
        
        # Stand button
        self.stand_btn = ttk.Button(
            self.basic_actions_frame,
            text="Stand",
            command=lambda: self.take_action(GameAction.STAND),
            style='Action.TButton',
            width=12
        )
        
        # Double Down button
        self.double_btn = ttk.Button(
            self.basic_actions_frame,
            text="Double Down",
            command=lambda: self.take_action(GameAction.DOUBLE),
            style='Action.TButton',
            width=12
        )
        
        # Advanced action buttons
        self.advanced_actions_frame = ttk.Frame(self.actions_frame)
        
        # Split button
        self.split_btn = ttk.Button(
            self.advanced_actions_frame,
            text="Split",
            command=lambda: self.take_action(GameAction.SPLIT),
            style='Action.TButton',
            width=12
        )
        
        # Surrender button
        self.surrender_btn = ttk.Button(
            self.advanced_actions_frame,
            text="Surrender",
            command=lambda: self.take_action(GameAction.SURRENDER),
            style='Action.TButton',
            width=12
        )
        
        # Insurance button
        self.insurance_btn = ttk.Button(
            self.advanced_actions_frame,
            text="Insurance",
            command=lambda: self.take_action(GameAction.INSURANCE),
            style='Action.TButton',
            width=12
        )
        
        # Store button references
        self.action_buttons = {
            GameAction.HIT: self.hit_btn,
            GameAction.STAND: self.stand_btn,
            GameAction.DOUBLE: self.double_btn,
            GameAction.SPLIT: self.split_btn,
            GameAction.SURRENDER: self.surrender_btn,
            GameAction.INSURANCE: self.insurance_btn
        }
        
    def create_side_bets_section(self):
        """Create side bets section."""
        self.side_bets_frame = ttk.LabelFrame(
            self.main_frame,
            text="Side Bets",
            padding="10"
        )
        
        # Perfect Pairs side bet
        self.perfect_pairs_frame = ttk.Frame(self.side_bets_frame)
        
        self.perfect_pairs_var = tk.BooleanVar()
        self.perfect_pairs_check = ttk.Checkbutton(
            self.perfect_pairs_frame,
            text="Perfect Pairs",
            variable=self.perfect_pairs_var,
            command=self.on_side_bet_change
        )
        
        self.perfect_pairs_amount_var = tk.DoubleVar(value=1.0)
        self.perfect_pairs_spinbox = ttk.Spinbox(
            self.perfect_pairs_frame,
            from_=1.0,
            to=25.0,
            increment=1.0,
            textvariable=self.perfect_pairs_amount_var,
            width=8,
            state='disabled'
        )
        
        # 21+3 side bet
        self.twentyone_plus_three_frame = ttk.Frame(self.side_bets_frame)
        
        self.twentyone_plus_three_var = tk.BooleanVar()
        self.twentyone_plus_three_check = ttk.Checkbutton(
            self.twentyone_plus_three_frame,
            text="21+3",
            variable=self.twentyone_plus_three_var,
            command=self.on_side_bet_change
        )
        
        self.twentyone_plus_three_amount_var = tk.DoubleVar(value=1.0)
        self.twentyone_plus_three_spinbox = ttk.Spinbox(
            self.twentyone_plus_three_frame,
            from_=1.0,
            to=25.0,
            increment=1.0,
            textvariable=self.twentyone_plus_three_amount_var,
            width=8,
            state='disabled'
        )
        
    def create_game_info_section(self):
        """Create game information section."""
        self.info_frame = ttk.LabelFrame(
            self.main_frame,
            text="Game Information",
            padding="10"
        )
        
        # Game controls
        self.controls_frame = ttk.Frame(self.info_frame)
        
        self.new_game_btn = ttk.Button(
            self.controls_frame,
            text="New Game",
            command=self.start_new_game,
            style='Action.TButton'
        )
        
        self.reset_btn = ttk.Button(
            self.controls_frame,
            text="Reset",
            command=self.reset_game,
            style='Action.TButton'
        )
        
        # Game status display
        self.status_text = tk.Text(
            self.info_frame,
            height=6,
            width=40,
            wrap=tk.WORD,
            state='disabled'
        )
        
        self.status_scrollbar = ttk.Scrollbar(
            self.info_frame,
            orient='vertical',
            command=self.status_text.yview
        )
        self.status_text.configure(yscrollcommand=self.status_scrollbar.set)

    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)

        # Configure grid weights
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)

        # Title
        self.title_label.grid(row=0, column=0, pady=(0, 10))

        # Betting section
        self.betting_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
        self.main_bet_frame.grid(row=0, column=0, sticky='ew')
        self.insurance_frame.grid(row=1, column=0, sticky='ew', pady=(5, 0))
        self.bankroll_frame.grid(row=2, column=0, sticky='ew', pady=(5, 0))

        # Actions section
        self.actions_frame.grid(row=2, column=0, sticky='ew', pady=(0, 10))
        self.basic_actions_frame.grid(row=0, column=0, sticky='ew')
        self.advanced_actions_frame.grid(row=1, column=0, sticky='ew', pady=(10, 0))

        # Layout action buttons
        self.hit_btn.grid(row=0, column=0, padx=(0, 5))
        self.stand_btn.grid(row=0, column=1, padx=5)
        self.double_btn.grid(row=0, column=2, padx=(5, 0))

        self.split_btn.grid(row=0, column=0, padx=(0, 5))
        self.surrender_btn.grid(row=0, column=1, padx=5)
        self.insurance_btn.grid(row=0, column=2, padx=(5, 0))

        # Side bets section
        self.side_bets_frame.grid(row=3, column=0, sticky='ew', pady=(0, 10))
        self.perfect_pairs_frame.grid(row=0, column=0, sticky='ew')
        self.twentyone_plus_three_frame.grid(row=1, column=0, sticky='ew', pady=(5, 0))

        # Layout side bet controls
        self.perfect_pairs_check.grid(row=0, column=0, sticky='w')
        self.perfect_pairs_spinbox.grid(row=0, column=1, padx=(10, 0))

        self.twentyone_plus_three_check.grid(row=0, column=0, sticky='w')
        self.twentyone_plus_three_spinbox.grid(row=0, column=1, padx=(10, 0))

        # Game info section
        self.info_frame.grid(row=4, column=0, sticky='nsew')
        self.controls_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        self.new_game_btn.grid(row=0, column=0, padx=(0, 5))
        self.reset_btn.grid(row=0, column=1, padx=(5, 0))

        self.status_text.grid(row=1, column=0, sticky='nsew')
        self.status_scrollbar.grid(row=1, column=1, sticky='ns')

        # Configure grid weights for expansion
        self.main_frame.grid_rowconfigure(4, weight=1)
        self.info_frame.grid_rowconfigure(1, weight=1)
        self.info_frame.grid_columnconfigure(0, weight=1)

    def take_action(self, action: GameAction):
        """Take a game action."""
        if not self.current_game_state:
            self.show_message("No active game. Start a new game first.")
            return

        try:
            # Check if action is available
            available_actions = self.game.get_available_actions()
            if action not in available_actions:
                self.show_message(f"Action {action.value} is not available.")
                return

            # Handle special cases
            if action == GameAction.INSURANCE:
                self.handle_insurance()
                return
            elif action == GameAction.DOUBLE:
                if not self.can_afford_double():
                    self.show_message("Insufficient funds to double down.")
                    return
            elif action == GameAction.SPLIT:
                if not self.can_afford_split():
                    self.show_message("Insufficient funds to split.")
                    return

            # Take the action
            self.current_game_state = self.game.take_action(action)

            # Update display
            self.update_action_buttons()
            self.update_game_status()

            # Notify callback
            if self.on_action_taken:
                self.on_action_taken(action, self.current_game_state)

            # Check if game is over
            if self.current_game_state.game_over:
                self.handle_game_over()

        except Exception as e:
            self.show_message(f"Error taking action: {str(e)}")

    def handle_insurance(self):
        """Handle insurance bet."""
        if self.insurance_var.get() <= 0:
            self.show_message("Set insurance amount first.")
            return

        insurance_amount = self.insurance_var.get()
        if insurance_amount > self.bankroll:
            self.show_message("Insufficient funds for insurance bet.")
            return

        # Take insurance action
        self.current_game_state = self.game.take_action(GameAction.INSURANCE)
        self.insurance_bet = insurance_amount
        self.bankroll -= insurance_amount

        self.update_bankroll_display()
        self.update_action_buttons()
        self.update_game_status()

        self.show_message(f"Insurance bet placed: ${insurance_amount:.2f}")

    def can_afford_double(self) -> bool:
        """Check if player can afford to double down."""
        return self.bankroll >= self.bet_amount

    def can_afford_split(self) -> bool:
        """Check if player can afford to split."""
        return self.bankroll >= self.bet_amount

    def start_new_game(self):
        """Start a new game."""
        # Check if player can afford the bet
        if self.bet_var.get() > self.bankroll:
            self.show_message("Insufficient funds for this bet.")
            return

        # Deduct bet from bankroll
        self.bet_amount = self.bet_var.get()
        self.bankroll -= self.bet_amount

        # Reset side bets
        self.insurance_bet = 0.0
        self.side_bets = {}

        # Place side bets if selected
        if self.perfect_pairs_var.get():
            side_bet_amount = self.perfect_pairs_amount_var.get()
            if side_bet_amount <= self.bankroll:
                self.side_bets['perfect_pairs'] = side_bet_amount
                self.bankroll -= side_bet_amount
            else:
                self.show_message("Insufficient funds for Perfect Pairs side bet.")

        if self.twentyone_plus_three_var.get():
            side_bet_amount = self.twentyone_plus_three_amount_var.get()
            if side_bet_amount <= self.bankroll:
                self.side_bets['21+3'] = side_bet_amount
                self.bankroll -= side_bet_amount
            else:
                self.show_message("Insufficient funds for 21+3 side bet.")

        # Start new game
        self.current_game_state = self.game.start_new_game(bet_amount=self.bet_amount)

        # Update displays
        self.update_bankroll_display()
        self.update_action_buttons()
        self.update_game_status()

        # Check for immediate insurance opportunity
        if (self.current_game_state.dealer_hand.cards and
            self.current_game_state.dealer_hand.cards[0].rank.value == 1):  # Ace
            self.enable_insurance()

        # Notify callback
        if self.on_game_state_change:
            self.on_game_state_change(self.current_game_state)

        self.show_message("New game started!")

    def reset_game(self):
        """Reset the current game."""
        self.current_game_state = None
        self.insurance_bet = 0.0
        self.side_bets = {}

        # Reset UI
        self.update_action_buttons()
        self.disable_insurance()
        self.clear_game_status()

        self.show_message("Game reset.")

    def update_action_buttons(self):
        """Update the state of action buttons based on available actions."""
        if not self.current_game_state:
            # Disable all action buttons
            for button in self.action_buttons.values():
                button.configure(state='disabled')
            return

        # Get available actions
        available_actions = self.game.get_available_actions()

        # Update button states
        for action, button in self.action_buttons.items():
            if action in available_actions:
                button.configure(state='normal')
            else:
                button.configure(state='disabled')

    def enable_insurance(self):
        """Enable insurance betting."""
        self.insurance_spinbox.configure(state='normal')
        max_insurance = self.bet_amount / 2  # Standard insurance is half the main bet
        self.insurance_var.set(max_insurance)

    def disable_insurance(self):
        """Disable insurance betting."""
        self.insurance_spinbox.configure(state='disabled')
        self.insurance_var.set(0.0)

    def update_bankroll_display(self):
        """Update the bankroll display."""
        self.bankroll_label.configure(text=f"${self.bankroll:.2f}")

    def update_game_status(self):
        """Update the game status display."""
        if not self.current_game_state:
            return

        status_text = self.get_game_status_text()

        # Update status display
        self.status_text.configure(state='normal')
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, status_text)
        self.status_text.configure(state='disabled')

    def get_game_status_text(self) -> str:
        """Get formatted game status text."""
        if not self.current_game_state:
            return "No active game."

        status_lines = []

        # Current hand info
        current_hand_index = self.current_game_state.current_hand_index
        if current_hand_index < len(self.current_game_state.player_hands):
            current_hand = self.current_game_state.player_hands[current_hand_index]
            status_lines.append(f"Current Hand {current_hand_index + 1}: {current_hand.get_value()}")

        # All hands summary
        for i, hand in enumerate(self.current_game_state.player_hands):
            hand_status = f"Hand {i + 1}: {hand.get_value()}"
            if hand.is_blackjack():
                hand_status += " (Blackjack!)"
            elif hand.is_bust():
                hand_status += " (Bust)"
            status_lines.append(hand_status)

        # Dealer hand
        dealer_value = self.current_game_state.dealer_hand.get_value()
        status_lines.append(f"Dealer: {dealer_value}")

        # Betting info
        status_lines.append(f"Main Bet: ${self.bet_amount:.2f}")
        if self.insurance_bet > 0:
            status_lines.append(f"Insurance: ${self.insurance_bet:.2f}")

        # Side bets
        for side_bet_name, amount in self.side_bets.items():
            status_lines.append(f"{side_bet_name}: ${amount:.2f}")

        # Available actions
        available_actions = self.game.get_available_actions()
        if available_actions:
            actions_text = ", ".join([action.value.title() for action in available_actions])
            status_lines.append(f"Available: {actions_text}")

        return "\n".join(status_lines)

    def clear_game_status(self):
        """Clear the game status display."""
        self.status_text.configure(state='normal')
        self.status_text.delete(1.0, tk.END)
        self.status_text.configure(state='disabled')

    def handle_game_over(self):
        """Handle game over state."""
        # Calculate payouts
        total_payout = self.calculate_payouts()

        # Update bankroll
        self.bankroll += total_payout
        self.update_bankroll_display()

        # Show results
        self.show_game_results(total_payout)

        # Disable action buttons
        for button in self.action_buttons.values():
            button.configure(state='disabled')

    def calculate_payouts(self) -> float:
        """Calculate total payouts for the game."""
        if not self.current_game_state or not self.current_game_state.game_over:
            return 0.0

        total_payout = 0.0

        # Main hand payouts
        for i, result in enumerate(self.current_game_state.results):
            hand_bet = self.bet_amount  # Each split hand has same bet

            if result == GameResult.PLAYER_WIN:
                total_payout += hand_bet * 2  # Return bet + winnings
            elif result == GameResult.PLAYER_BLACKJACK:
                total_payout += hand_bet * 2.5  # 3:2 payout + return bet
            elif result == GameResult.PUSH:
                total_payout += hand_bet  # Return bet only
            # Losses: no payout

        # Insurance payout
        if self.insurance_bet > 0:
            dealer_has_blackjack = self.current_game_state.dealer_hand.is_blackjack()
            if dealer_has_blackjack:
                total_payout += self.insurance_bet * 3  # 2:1 payout + return bet

        # Side bet payouts
        total_payout += self.calculate_side_bet_payouts()

        return total_payout

    def calculate_side_bet_payouts(self) -> float:
        """Calculate side bet payouts."""
        total_payout = 0.0

        if not self.current_game_state or not self.current_game_state.player_hands:
            return total_payout

        player_hand = self.current_game_state.player_hands[0]  # First hand for side bets
        dealer_upcard = self.current_game_state.dealer_hand.cards[0] if self.current_game_state.dealer_hand.cards else None

        # Perfect Pairs
        if 'perfect_pairs' in self.side_bets:
            bet_amount = self.side_bets['perfect_pairs']
            if self.check_perfect_pairs(player_hand):
                # Simplified: 6:1 payout for any pair
                total_payout += bet_amount * 7  # 6:1 + return bet

        # 21+3
        if '21+3' in self.side_bets:
            bet_amount = self.side_bets['21+3']
            if self.check_twentyone_plus_three(player_hand, dealer_upcard):
                # Simplified: 9:1 payout for any qualifying combination
                total_payout += bet_amount * 10  # 9:1 + return bet

        return total_payout

    def check_perfect_pairs(self, hand) -> bool:
        """Check if hand qualifies for Perfect Pairs side bet."""
        if len(hand.cards) >= 2:
            return hand.cards[0].rank == hand.cards[1].rank
        return False

    def check_twentyone_plus_three(self, player_hand, dealer_upcard) -> bool:
        """Check if cards qualify for 21+3 side bet."""
        if len(player_hand.cards) >= 2 and dealer_upcard:
            # Simplified: check for flush, straight, or three of a kind
            cards = player_hand.cards[:2] + [dealer_upcard]
            ranks = [card.min_value for card in cards]  # Use min_value for numeric comparison
            suits = [card.suit for card in cards]

            # Check for flush (all same suit)
            if len(set(suits)) == 1:
                return True

            # Check for straight (simplified - just check if consecutive)
            ranks.sort()
            if len(ranks) == 3 and ranks[2] - ranks[0] == 2 and ranks[1] - ranks[0] == 1:
                return True

            # Check for three of a kind
            if len(set(ranks)) == 1:
                return True

        return False

    def show_game_results(self, total_payout: float):
        """Show game results to the user."""
        net_result = total_payout - self.bet_amount - sum(self.side_bets.values()) - self.insurance_bet

        result_text = f"Game Over!\n\n"
        result_text += f"Total Payout: ${total_payout:.2f}\n"
        result_text += f"Net Result: ${net_result:+.2f}\n"
        result_text += f"New Bankroll: ${self.bankroll:.2f}"

        messagebox.showinfo("Game Results", result_text)

    def show_message(self, message: str):
        """Show a message to the user."""
        # Could be enhanced to show in status bar or dedicated message area
        print(f"Game Message: {message}")  # For now, print to console

    def on_bet_change(self):
        """Handle bet amount change."""
        self.bet_amount = self.bet_var.get()

    def on_side_bet_change(self):
        """Handle side bet selection change."""
        # Enable/disable side bet amount spinboxes
        if self.perfect_pairs_var.get():
            self.perfect_pairs_spinbox.configure(state='normal')
        else:
            self.perfect_pairs_spinbox.configure(state='disabled')

        if self.twentyone_plus_three_var.get():
            self.twentyone_plus_three_spinbox.configure(state='normal')
        else:
            self.twentyone_plus_three_spinbox.configure(state='disabled')

    def get_current_game_state(self) -> Optional[GameState]:
        """Get the current game state."""
        return self.current_game_state

    def set_bankroll(self, amount: float):
        """Set the bankroll amount."""
        self.bankroll = amount
        self.update_bankroll_display()
