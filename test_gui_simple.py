#!/usr/bin/env python3
"""
Simple GUI test for BlackJack Bot ML Phase 4.

This script tests the GUI components without launching the full interface.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all GUI imports."""
    print("🧪 Testing GUI imports...")
    
    try:
        import tkinter as tk
        print("  ✅ tkinter imported")
        
        from gui.main_window import BlackjackGUI
        print("  ✅ main_window imported")
        
        from gui.card_selector import CardSelectorWidget
        print("  ✅ card_selector imported")
        
        from gui.game_display import GameDisplayWidget
        print("  ✅ game_display imported")
        
        from gui.advice_engine import AdviceEngineGUI
        print("  ✅ advice_engine imported")
        
        from gui.config_panel import ConfigPanelWidget
        print("  ✅ config_panel imported")
        
        print("✅ All GUI imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_functionality():
    """Test core functionality without GUI."""
    print("\n🧪 Testing core functionality...")
    
    try:
        from core.card import Card, Suit, Rank
        from core.game_logic import BlackjackGame, GameAction
        from agents.basic_strategy_agent import BasicStrategyAgent
        
        # Test card creation
        card = Card(Suit.HEARTS, Rank.ACE)
        print(f"  ✅ Card created: {card}")
        
        # Test game creation
        game = BlackjackGame(num_decks=6)
        print("  ✅ Game created")
        
        # Test agent creation
        agent = BasicStrategyAgent("Test Agent")
        print(f"  ✅ Agent created: {agent.name}")
        
        # Test game state
        game_state = game.start_new_game(bet_amount=10.0)
        print(f"  ✅ Game state created: {len(game_state.player_hands)} hands")
        
        # Test agent decision
        action = agent.get_action(game_state)
        print(f"  ✅ Agent decision: {action.value}")
        
        print("✅ Core functionality working!")
        return True
        
    except Exception as e:
        print(f"❌ Core functionality failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_integration():
    """Test agent integration for GUI."""
    print("\n🧪 Testing agent integration...")
    
    try:
        from agents.basic_strategy_agent import BasicStrategyAgent
        from personas.human_persona_agent import HumanPersonaAgent
        from personas.cautious_persona import CautiousPersona
        from personas.aggressive_persona import AggressivePersona
        from personas.intuitive_persona import IntuitivePersona
        
        # Test basic strategy agent
        bs_agent = BasicStrategyAgent("Basic Strategy")
        print(f"  ✅ Basic Strategy agent: {bs_agent.name}")
        
        # Test persona agents
        cautious_agent = HumanPersonaAgent("Cautious", CautiousPersona())
        print(f"  ✅ Cautious agent: {cautious_agent.name}")
        
        aggressive_agent = HumanPersonaAgent("Aggressive", AggressivePersona())
        print(f"  ✅ Aggressive agent: {aggressive_agent.name}")
        
        intuitive_agent = HumanPersonaAgent("Intuitive", IntuitivePersona())
        print(f"  ✅ Intuitive agent: {intuitive_agent.name}")
        
        # Test persona switcher
        from personas.persona_switcher_agent import PersonaSwitcherAgent
        from personas.persona_switcher import SwitchConfig
        
        switch_config = SwitchConfig(min_hands_per_persona=5, max_hands_per_persona=15)
        switcher_agent = PersonaSwitcherAgent("Switcher", switch_config)
        switcher_agent.switcher.add_persona("cautious", CautiousPersona())
        switcher_agent.switcher.add_persona("aggressive", AggressivePersona())
        print(f"  ✅ Switcher agent: {switcher_agent.name}")
        
        print("✅ Agent integration working!")
        return True
        
    except Exception as e:
        print(f"❌ Agent integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """Test GUI components creation."""
    print("\n🧪 Testing GUI components...")
    
    try:
        import tkinter as tk
        
        # Create root window (hidden)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Test card selector
        from gui.card_selector import CardSelectorWidget
        frame1 = tk.Frame(root)
        card_selector = CardSelectorWidget(frame1)
        print("  ✅ Card selector created")
        
        # Test game display
        from gui.game_display import GameDisplayWidget
        frame2 = tk.Frame(root)
        game_display = GameDisplayWidget(frame2)
        print("  ✅ Game display created")
        
        # Test advice engine
        from gui.advice_engine import AdviceEngineGUI
        frame3 = tk.Frame(root)
        advice_engine = AdviceEngineGUI(frame3)
        print("  ✅ Advice engine created")
        
        # Test config panel
        from gui.config_panel import ConfigPanelWidget
        frame4 = tk.Frame(root)
        config_panel = ConfigPanelWidget(frame4)
        print("  ✅ Config panel created")
        
        # Clean up
        root.destroy()
        
        print("✅ GUI components working!")
        return True
        
    except Exception as e:
        print(f"❌ GUI components failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🎯 BlackJack Bot ML - Phase 4 GUI Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_core_functionality,
        test_agent_integration,
        test_gui_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            break  # Stop on first failure
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Phase 4 GUI Interface is ready!")
        print("\nTo launch the GUI:")
        print("  python run_gui.py")
        return True
    else:
        print("❌ Some tests failed")
        print("Please fix the issues before launching the GUI")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
