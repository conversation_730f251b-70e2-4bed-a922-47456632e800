"""
Basic Strategy Persona for Black<PERSON><PERSON>t ML.

This persona represents a highly skilled player who:
- Follows Basic Strategy with 99% accuracy (near-perfect)
- Makes decisions at moderate speed
- Has minimal emotional influence
- Represents the "expert" baseline for persona switching
"""

import random
from .base_persona import <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON>, DecisionContext, ErrorType
from core.hand import Hand
from core.game_logic import GameState
from utils.basic_strategy_charts import BasicStrategyAction


def create_basic_strategy_persona_config() -> PersonaConfig:
    """Create configuration for the Basic Strategy persona."""
    
    decision_pattern = DecisionPattern(
        base_accuracy=0.99,  # Near-perfect accuracy
        decision_speed=1.0,  # Normal decision speed
        consistency=0.98,    # Very consistent
        aggression_bias=0.0,  # No bias - follows optimal strategy
        risk_tolerance=0.5,  # Neutral risk tolerance
        emotional_volatility=0.01,  # Minimal emotional influence
        fatigue_rate=0.0001,  # Very slow fatigue accumulation
        
        # Timing parameters - consistent, moderate speed
        min_decision_time=0.8,
        max_decision_time=6.0,
        avg_decision_time=2.0,
        timing_variance=0.1  # Low variance - very consistent timing
    )
    
    # Error patterns - minimal errors, mostly timing inconsistencies
    error_patterns = {
        ErrorType.STRATEGY_DEVIATION: 0.005,  # 0.5% strategy errors (very rare)
        ErrorType.TIMING_INCONSISTENCY: 0.02,  # 2% timing variation
        ErrorType.EMOTIONAL_DECISION: 0.001,  # Extremely rare emotional decisions
        ErrorType.FATIGUE_ERROR: 0.001,  # Very rare fatigue errors
        ErrorType.DISTRACTION_ERROR: 0.003  # Rare distractions
    }
    
    # Context modifiers - minimal impact from external factors
    context_modifiers = {
        DecisionContext.PRESSURE: {
            "accuracy_modifier": -0.01,  # Minimal accuracy drop under pressure
            "speed_modifier": 1.05,  # Slightly slower when pressured
            "aggression_modifier": 0.0  # No change in strategy
        },
        DecisionContext.CONFIDENT: {
            "accuracy_modifier": 0.005,  # Tiny improvement when confident
            "speed_modifier": 0.95,  # Slightly faster
            "aggression_modifier": 0.0  # No change in strategy
        },
        DecisionContext.TIRED: {
            "accuracy_modifier": -0.02,  # Small accuracy drop when tired
            "speed_modifier": 1.1,  # Slightly slower when tired
            "aggression_modifier": 0.0  # No change in strategy
        },
        DecisionContext.DISTRACTED: {
            "accuracy_modifier": -0.03,  # Small accuracy drop when distracted
            "speed_modifier": 1.15,  # Slower when distracted
            "aggression_modifier": 0.0  # No change in strategy
        }
    }
    
    return PersonaConfig(
        name="Basic Strategy Expert",
        description="A highly skilled player who follows Basic Strategy with near-perfect accuracy and minimal emotional influence",
        decision_pattern=decision_pattern,
        error_patterns=error_patterns,
        context_modifiers=context_modifiers
    )


class BasicStrategyPersona(BasePersona):
    """
    Basic Strategy persona implementation.
    
    This persona represents a highly skilled player who:
    - Has studied Basic Strategy extensively and follows it almost perfectly
    - Makes decisions at consistent, moderate speed
    - Is minimally affected by emotions or external factors
    - Serves as the "expert" baseline in persona switching scenarios
    """
    
    def __init__(self):
        """Initialize the Basic Strategy persona."""
        config = create_basic_strategy_persona_config()
        super().__init__(config)
    
    def _apply_persona_bias(self, optimal_action: BasicStrategyAction,
                          player_hand: Hand, dealer_upcard: int,
                          game_state: GameState) -> BasicStrategyAction:
        """
        Apply minimal bias to decision making.
        
        The Basic Strategy persona has virtually no bias - it follows
        optimal strategy with only extremely rare deviations.
        """
        # 99% of the time, return the optimal action unchanged
        if random.random() < 0.99:
            return optimal_action
        
        # Very rare "human moment" - tiny deviation from optimal play
        # This represents the 1% of cases where even experts might make a small error
        return self._apply_minimal_deviation(optimal_action, player_hand, dealer_upcard)
    
    def _apply_minimal_deviation(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Apply extremely minimal deviation from optimal play.
        
        Even experts occasionally have tiny lapses in judgment.
        """
        hand_value = player_hand.get_value()
        
        # Only apply deviations in very specific, borderline situations
        # where even experts might occasionally second-guess themselves
        
        if optimal_action == BasicStrategyAction.HIT:
            # Extremely rare case: stand on 16 vs 10 (fear of busting)
            if hand_value == 16 and dealer_upcard == 10 and random.random() < 0.1:
                return BasicStrategyAction.STAND
        
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Extremely rare case: just hit instead of double on marginal doubles
            if (hand_value == 11 and dealer_upcard in [10, 11] and 
                random.random() < 0.05):
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Extremely rare case: don't split 8s vs Ace (risk aversion)
            if (player_hand.is_pair() and 
                player_hand.cards[0].get_value() == 8 and 
                dealer_upcard == 11 and 
                random.random() < 0.05):
                return BasicStrategyAction.HIT
        
        # In all other cases, stick with optimal action
        return optimal_action
    
    def _strategy_deviation_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate strategy deviation errors (extremely rare for this persona).
        
        Basic Strategy persona errors are minimal and typically involve
        overthinking in complex situations.
        """
        hand_value = player_hand.get_value()
        
        # Even errors tend to be conservative for this persona
        if optimal_action == BasicStrategyAction.HIT:
            # Rare error: stand on stiff hands vs strong dealer (overthinking)
            if hand_value in [12, 13, 14, 15, 16] and dealer_upcard >= 7:
                if random.random() < 0.3:  # 30% chance of this type of error
                    return BasicStrategyAction.STAND
        
        elif optimal_action == BasicStrategyAction.DOUBLE:
            # Rare error: hit instead of double (second-guessing)
            return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # Rare error: don't split (complexity avoidance)
            if hand_value <= 11:
                return BasicStrategyAction.HIT
            else:
                return BasicStrategyAction.STAND
        
        return optimal_action
    
    def _emotional_decision_error(self, optimal_action: BasicStrategyAction,
                                player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate emotional decision errors (extremely rare for this persona).
        
        Basic Strategy persona is highly disciplined and rarely makes
        emotional decisions.
        """
        # Even emotional decisions are minimal and conservative
        if self.consecutive_losses >= 5:  # Only after significant losses
            hand_value = player_hand.get_value()
            
            # Slight tendency toward more conservative play after losses
            if optimal_action == BasicStrategyAction.HIT and hand_value >= 12:
                if random.random() < 0.1:  # 10% chance
                    return BasicStrategyAction.STAND
        
        return optimal_action
    
    def _fatigue_error(self, optimal_action: BasicStrategyAction,
                     player_hand: Hand, dealer_upcard: int) -> BasicStrategyAction:
        """
        Generate fatigue-induced errors (very rare for this persona).
        
        Basic Strategy persona has good discipline even when tired.
        """
        # Fatigue leads to slightly more conservative decisions
        if optimal_action == BasicStrategyAction.DOUBLE:
            # When tired, sometimes just hit instead of double
            if random.random() < 0.2:  # 20% chance when fatigue error occurs
                return BasicStrategyAction.HIT
        
        elif optimal_action == BasicStrategyAction.SPLIT:
            # When tired, sometimes avoid splitting
            if random.random() < 0.15:  # 15% chance when fatigue error occurs
                hand_value = player_hand.get_value()
                if hand_value <= 11:
                    return BasicStrategyAction.HIT
                else:
                    return BasicStrategyAction.STAND
        
        return optimal_action
    
    def get_persona_description(self) -> str:
        """Get a description of this persona's characteristics."""
        return (
            "Basic Strategy Expert: A highly skilled player with near-perfect "
            "knowledge of optimal blackjack strategy. Makes decisions with 99% "
            "accuracy and minimal emotional influence. Represents the expert "
            "baseline for comparison with other playing styles."
        )
    
    def get_skill_level(self) -> str:
        """Get the skill level of this persona."""
        return "Expert"
    
    def get_accuracy_range(self) -> tuple:
        """Get the expected accuracy range for this persona."""
        return (0.98, 0.995)  # 98-99.5% accuracy range
