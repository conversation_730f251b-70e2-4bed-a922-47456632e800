"""
Complete blackjack game logic for BlackJack Bot ML.

This module implements the full blackjack game engine with all player actions
and proper rule enforcement with flexible rule configuration support.
"""

from enum import Enum
from typing import List, Optional, Tuple, Dict, Any
from .card import Card
from .deck import Deck
from .hand import Hand
from .game_rules import BlackjackRules, get_realistic_rules


class GameAction(Enum):
    """Available player actions in blackjack."""
    HIT = "hit"
    STAND = "stand"
    DOUBLE = "double"
    SPLIT = "split"
    SURRENDER = "surrender"
    INSURANCE = "insurance"


class GameResult(Enum):
    """Possible game outcomes."""
    PLAYER_WIN = "player_win"
    DEALER_WIN = "dealer_win"
    PUSH = "push"
    PLAYER_BLACKJACK = "player_blackjack"
    DEALER_BLACKJACK = "dealer_blackjack"
    PLAYER_BUST = "player_bust"
    DEALER_BUST = "dealer_bust"


class GameState:
    """Represents the current state of a blackjack game."""

    def __init__(self):
        self.player_hands: List[Hand] = []
        self.dealer_hand: Hand = Hand()
        self.current_hand_index: int = 0
        self.game_over: bool = False
        self.results: List[GameResult] = []
        self.bet_amount: float = 0
        self.can_double: List[bool] = []
        self.can_split: List[bool] = []
        self.can_surrender: bool = False
        self.can_insurance: bool = False
        self.insurance_bet: float = 0
        self.surrendered: bool = False


class BlackjackGame:
    """
    Complete blackjack game implementation with flexible rule support.

    Supports multiple blackjack variants and casino rule configurations.
    """

    def __init__(self, rules: Optional[BlackjackRules] = None):
        """
        Initialize the blackjack game.

        Args:
            rules: Game rules configuration (default: realistic online casino rules)
        """
        self.rules = rules if rules is not None else get_realistic_rules()
        self.deck = Deck(
            num_decks=self.rules.num_decks,
            shuffle_after_each_hand=self.rules.continuous_shuffle
        )
        self.state = GameState()

        # Derived rule properties for quick access
        self._max_hands_after_split = self._calculate_max_hands()

    def _calculate_max_hands(self) -> int:
        """Calculate maximum hands after splitting based on rules."""
        from .game_rules import SplitRule
        if self.rules.split_rule == SplitRule.UNLIMITED:
            return 4  # Practical limit
        elif self.rules.split_rule == SplitRule.THREE_HANDS:
            return 3
        elif self.rules.split_rule == SplitRule.TWO_HANDS:
            return 2
        else:  # NO_SPLIT
            return 1
    
    def start_new_game(self, bet_amount: float = 1.0) -> GameState:
        """
        Start a new blackjack game.
        
        Args:
            bet_amount: The bet amount for this game
            
        Returns:
            The initial game state
        """
        # Reset game state
        self.state = GameState()
        self.state.bet_amount = bet_amount
        
        # Deal initial cards
        player_hand = Hand()
        dealer_hand = Hand()
        
        # Deal two cards to player, two to dealer (one face down)
        player_hand.add_card(self.deck.deal_card())
        dealer_hand.add_card(self.deck.deal_card())
        player_hand.add_card(self.deck.deal_card())
        dealer_hand.add_card(self.deck.deal_card())
        
        self.state.player_hands = [player_hand]
        self.state.dealer_hand = dealer_hand
        self.state.current_hand_index = 0
        
        # Initialize action availability
        self.state.can_double = [self._can_double_hand(player_hand)]
        self.state.can_split = [self._can_split_hand(player_hand)]
        self.state.can_surrender = self._can_surrender()
        self.state.can_insurance = self._can_insurance()

        # Check for immediate blackjacks
        if player_hand.is_blackjack() or dealer_hand.is_blackjack():
            self._resolve_blackjacks()

        return self.state

    def _can_double_hand(self, hand: Hand) -> bool:
        """Check if a hand can double down based on rules."""
        from .game_rules import DoubleRule

        if not hand.can_double():
            return False

        hand_value = hand.get_value()

        if self.rules.double_rule == DoubleRule.ANY_TWO_CARDS:
            return True
        elif self.rules.double_rule == DoubleRule.NINE_TEN_ELEVEN:
            return hand_value in [9, 10, 11]
        elif self.rules.double_rule == DoubleRule.TEN_ELEVEN:
            return hand_value in [10, 11]
        else:  # NO_DOUBLE
            return False

    def _can_split_hand(self, hand: Hand) -> bool:
        """Check if a hand can be split based on rules."""
        from .game_rules import SplitRule

        if not hand.can_split():
            return False

        if self.rules.split_rule == SplitRule.NO_SPLIT:
            return False

        # Check if we've reached the split limit
        if len(self.state.player_hands) >= self._max_hands_after_split:
            return False

        # Check resplit aces rule
        if hand._split_from_aces and not self.rules.resplit_aces:
            return False

        return True

    def _can_surrender(self) -> bool:
        """Check if surrender is available based on rules."""
        from .game_rules import SurrenderRule

        if self.rules.surrender_rule == SurrenderRule.NO_SURRENDER:
            return False

        # Surrender only available on first two cards
        if len(self.state.player_hands) != 1 or len(self.state.player_hands[0].cards) != 2:
            return False

        # Early surrender vs late surrender logic would go here
        return True

    def _can_insurance(self) -> bool:
        """Check if insurance is available based on rules."""
        if not self.rules.insurance_available:
            return False

        # Insurance only available when dealer shows ace
        if not self.state.dealer_hand.cards:
            return False

        dealer_upcard = self.state.dealer_hand.cards[0]
        return dealer_upcard.is_ace

    def get_available_actions(self, hand_index: int = None) -> List[GameAction]:
        """
        Get available actions for the current or specified hand.
        
        Args:
            hand_index: Index of hand to check (default: current hand)
            
        Returns:
            List of available actions
        """
        if hand_index is None:
            hand_index = self.state.current_hand_index
        
        if (hand_index >= len(self.state.player_hands) or 
            self.state.game_over):
            return []
        
        hand = self.state.player_hands[hand_index]
        actions = []
        
        # Basic actions always available (unless bust or blackjack)
        if not hand.is_bust() and not hand.is_blackjack():
            actions.extend([GameAction.HIT, GameAction.STAND])
        
        # Double down
        if (hand_index < len(self.state.can_double) and
            self.state.can_double[hand_index] and
            self._can_double_hand(hand)):
            actions.append(GameAction.DOUBLE)

        # Split
        if (hand_index < len(self.state.can_split) and
            self.state.can_split[hand_index] and
            self._can_split_hand(hand)):
            actions.append(GameAction.SPLIT)

        # Surrender (only for current hand and first decision)
        if (hand_index == self.state.current_hand_index and
            self.state.can_surrender and
            len(hand.cards) == 2):
            actions.append(GameAction.SURRENDER)

        # Insurance (only when dealer shows ace and first decision)
        if (hand_index == 0 and
            self.state.can_insurance and
            self.state.insurance_bet == 0):
            actions.append(GameAction.INSURANCE)

        return actions
    
    def take_action(self, action: GameAction) -> GameState:
        """
        Execute a player action on the current hand.
        
        Args:
            action: The action to take
            
        Returns:
            Updated game state
            
        Raises:
            ValueError: If action is not available
        """
        if self.state.game_over:
            raise ValueError("Game is already over")
        
        available_actions = self.get_available_actions()
        if action not in available_actions:
            raise ValueError(f"Action {action} not available. Available: {available_actions}")
        
        current_hand = self.state.player_hands[self.state.current_hand_index]
        
        if action == GameAction.HIT:
            self._hit(current_hand)
        elif action == GameAction.STAND:
            self._stand()
        elif action == GameAction.DOUBLE:
            self._double(current_hand)
        elif action == GameAction.SPLIT:
            self._split(current_hand)
        elif action == GameAction.SURRENDER:
            self._surrender()
        elif action == GameAction.INSURANCE:
            self._insurance()
        
        # Check if current hand is finished
        if (current_hand.is_bust() or
            current_hand.is_blackjack() or
            action in [GameAction.STAND, GameAction.DOUBLE, GameAction.SURRENDER]):
            self._advance_to_next_hand()

        # Insurance doesn't advance hand

        return self.state
    
    def _hit(self, hand: Hand) -> None:
        """Add a card to the specified hand."""
        card = self.deck.deal_card()
        if card:
            hand.add_card(card)
    
    def _stand(self) -> None:
        """Stand on current hand (no action needed)."""
        pass
    
    def _double(self, hand: Hand) -> None:
        """Double down on the specified hand."""
        # Double the bet
        self.state.bet_amount *= 2
        
        # Deal exactly one more card
        self._hit(hand)
        
        # Disable further doubling for this hand
        if self.state.current_hand_index < len(self.state.can_double):
            self.state.can_double[self.state.current_hand_index] = False
    
    def _split(self, hand: Hand) -> None:
        """Split the specified hand into two hands."""
        # Create new hand from split
        new_hand = hand.split()
        
        # Insert new hand after current hand
        insert_index = self.state.current_hand_index + 1
        self.state.player_hands.insert(insert_index, new_hand)
        
        # Deal one card to each hand
        self._hit(hand)
        self._hit(new_hand)
        
        # Update action availability
        can_double_new = self.rules.double_after_split and self._can_double_hand(new_hand)
        can_split_new = self._can_split_hand(new_hand)
        
        self.state.can_double.insert(insert_index, can_double_new)
        self.state.can_split.insert(insert_index, can_split_new)
        
        # Update current hand's split availability
        if self.state.current_hand_index < len(self.state.can_split):
            can_split_current = self._can_split_hand(hand)
            self.state.can_split[self.state.current_hand_index] = can_split_current

    def _surrender(self) -> None:
        """Surrender the current hand."""
        self.state.surrendered = True
        self.state.game_over = True
        # Surrender loses half the bet
        self.state.results = [GameResult.DEALER_WIN]  # Will be handled in reward calculation

    def _insurance(self) -> None:
        """Take insurance bet."""
        # Insurance bet is half the original bet
        self.state.insurance_bet = self.state.bet_amount / 2
        self.state.can_insurance = False  # Can only take insurance once
    
    def _advance_to_next_hand(self) -> None:
        """Move to the next player hand or finish player turn."""
        self.state.current_hand_index += 1
        
        if self.state.current_hand_index >= len(self.state.player_hands):
            # All player hands finished, play dealer
            self._play_dealer()
            self._resolve_game()
    
    def _play_dealer(self) -> None:
        """Play the dealer's hand according to rules."""
        dealer_hand = self.state.dealer_hand
        
        # Dealer plays only if at least one player hand is not bust
        player_has_valid_hand = any(not hand.is_bust() 
                                  for hand in self.state.player_hands)
        
        if not player_has_valid_hand:
            return  # No need to play if all players bust
        
        # Dealer hits until 17 or higher (hits soft 17 in our rules)
        while self._dealer_should_hit(dealer_hand):
            self._hit(dealer_hand)
    
    def _dealer_should_hit(self, dealer_hand: Hand) -> bool:
        """Determine if dealer should hit based on rules."""
        from .game_rules import DealerRule

        value = dealer_hand.get_value()

        if value < 17:
            return True
        elif value == 17:
            if self.rules.dealer_rule == DealerRule.HIT_SOFT_17 and dealer_hand.is_soft():
                return True
            else:
                return False
        else:
            return False
    
    def _resolve_blackjacks(self) -> None:
        """Handle immediate blackjack resolution."""
        player_hand = self.state.player_hands[0]
        dealer_hand = self.state.dealer_hand
        
        if player_hand.is_blackjack() and dealer_hand.is_blackjack():
            self.state.results = [GameResult.PUSH]
        elif player_hand.is_blackjack():
            self.state.results = [GameResult.PLAYER_BLACKJACK]
        elif dealer_hand.is_blackjack():
            self.state.results = [GameResult.DEALER_BLACKJACK]
        
        self.state.game_over = True
    
    def _resolve_game(self) -> None:
        """Resolve the final game results for all hands."""
        dealer_hand = self.state.dealer_hand
        dealer_value = dealer_hand.get_value()
        dealer_bust = dealer_hand.is_bust()
        dealer_blackjack = dealer_hand.is_blackjack()
        
        results = []
        
        for hand in self.state.player_hands:
            player_value = hand.get_value()
            player_bust = hand.is_bust()
            player_blackjack = hand.is_blackjack()
            
            if player_bust:
                results.append(GameResult.PLAYER_BUST)
            elif dealer_bust:
                results.append(GameResult.DEALER_BUST)
            elif player_blackjack and not dealer_blackjack:
                results.append(GameResult.PLAYER_BLACKJACK)
            elif dealer_blackjack and not player_blackjack:
                results.append(GameResult.DEALER_BLACKJACK)
            elif player_value > dealer_value:
                results.append(GameResult.PLAYER_WIN)
            elif dealer_value > player_value:
                results.append(GameResult.DEALER_WIN)
            else:
                results.append(GameResult.PUSH)
        
        self.state.results = results
        self.state.game_over = True
    
    def get_game_state(self) -> GameState:
        """Get the current game state."""
        return self.state
    
    def is_game_over(self) -> bool:
        """Check if the game is over."""
        return self.state.game_over
