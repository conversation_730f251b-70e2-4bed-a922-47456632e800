"""
Flexible blackjack rule configuration system for BlackJack Bot ML.

This module provides comprehensive rule configuration support for different
casino environments and blackjack variants.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, Any, Optional, Tuple


class BlackjackVariant(Enum):
    """Different blackjack game variants."""
    AMERICAN = "american"
    EUROPEAN = "european"
    ATLANTIC_CITY = "atlantic_city"
    LAS_VEGAS = "las_vegas"
    ONLINE_LIVE = "online_live"
    SINGLE_DECK = "single_deck"


class DealerRule(Enum):
    """Dealer behavior rules."""
    HIT_SOFT_17 = "hit_soft_17"
    STAND_SOFT_17 = "stand_soft_17"


class DoubleRule(Enum):
    """Double down rules."""
    ANY_TWO_CARDS = "any_two_cards"
    NINE_TEN_ELEVEN = "9_10_11"
    TEN_ELEVEN = "10_11"
    NO_DOUBLE = "no_double"


class SplitRule(Enum):
    """Splitting rules."""
    UNLIMITED = "unlimited"
    THREE_HANDS = "three_hands"
    TWO_HANDS = "two_hands"
    NO_SPLIT = "no_split"


class SurrenderRule(Enum):
    """Surrender rules."""
    EARLY = "early"
    LATE = "late"
    NO_SURRENDER = "no_surrender"


@dataclass
class BlackjackRules:
    """Comprehensive blackjack rule configuration."""
    
    # Basic game setup
    num_decks: int = 6
    deck_penetration: float = 0.75  # When to reshuffle (0.0-1.0)
    continuous_shuffle: bool = True  # CSM simulation
    
    # Dealer rules
    dealer_rule: DealerRule = DealerRule.HIT_SOFT_17
    dealer_peeks_blackjack: bool = True  # Check for dealer blackjack
    
    # Player action rules
    double_rule: DoubleRule = DoubleRule.ANY_TWO_CARDS
    double_after_split: bool = True
    split_rule: SplitRule = SplitRule.THREE_HANDS
    resplit_aces: bool = False
    hit_split_aces: bool = False
    surrender_rule: SurrenderRule = SurrenderRule.LATE
    
    # Payout rules
    blackjack_payout: float = 1.5  # 3:2 = 1.5, 6:5 = 1.2
    insurance_available: bool = True
    insurance_payout: float = 2.0  # 2:1 payout
    
    # Advanced rules
    charlie_rule: Optional[int] = None  # 5-card charlie, 6-card charlie, etc.
    suited_blackjack_bonus: float = 0.0  # Additional bonus for suited blackjack
    
    # Training-specific rules
    max_hands_per_episode: int = 1  # For RL training
    allow_negative_counts: bool = True  # For card counting simulation
    
    def __post_init__(self):
        """Validate rule configuration."""
        if not 1 <= self.num_decks <= 8:
            raise ValueError("Number of decks must be between 1 and 8")
        if not 0.0 <= self.deck_penetration <= 1.0:
            raise ValueError("Deck penetration must be between 0.0 and 1.0")
        if not 0.5 <= self.blackjack_payout <= 2.0:
            raise ValueError("Blackjack payout must be between 0.5 and 2.0")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert rules to dictionary for serialization."""
        return {
            'num_decks': self.num_decks,
            'deck_penetration': self.deck_penetration,
            'continuous_shuffle': self.continuous_shuffle,
            'dealer_rule': self.dealer_rule.value,
            'dealer_peeks_blackjack': self.dealer_peeks_blackjack,
            'double_rule': self.double_rule.value,
            'double_after_split': self.double_after_split,
            'split_rule': self.split_rule.value,
            'resplit_aces': self.resplit_aces,
            'hit_split_aces': self.hit_split_aces,
            'surrender_rule': self.surrender_rule.value,
            'blackjack_payout': self.blackjack_payout,
            'insurance_available': self.insurance_available,
            'insurance_payout': self.insurance_payout,
            'charlie_rule': self.charlie_rule,
            'suited_blackjack_bonus': self.suited_blackjack_bonus,
            'max_hands_per_episode': self.max_hands_per_episode,
            'allow_negative_counts': self.allow_negative_counts
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BlackjackRules':
        """Create rules from dictionary."""
        # Convert enum strings back to enums
        if 'dealer_rule' in data:
            data['dealer_rule'] = DealerRule(data['dealer_rule'])
        if 'double_rule' in data:
            data['double_rule'] = DoubleRule(data['double_rule'])
        if 'split_rule' in data:
            data['split_rule'] = SplitRule(data['split_rule'])
        if 'surrender_rule' in data:
            data['surrender_rule'] = SurrenderRule(data['surrender_rule'])
        
        return cls(**data)


# Predefined rule configurations for common casino environments
RULE_PRESETS: Dict[BlackjackVariant, BlackjackRules] = {
    BlackjackVariant.AMERICAN: BlackjackRules(
        num_decks=6,
        dealer_rule=DealerRule.HIT_SOFT_17,
        double_rule=DoubleRule.ANY_TWO_CARDS,
        double_after_split=True,
        split_rule=SplitRule.THREE_HANDS,
        resplit_aces=False,
        surrender_rule=SurrenderRule.LATE,
        blackjack_payout=1.5,
        insurance_available=True
    ),
    
    BlackjackVariant.EUROPEAN: BlackjackRules(
        num_decks=6,
        dealer_rule=DealerRule.STAND_SOFT_17,
        dealer_peeks_blackjack=False,  # No hole card
        double_rule=DoubleRule.NINE_TEN_ELEVEN,
        double_after_split=False,
        split_rule=SplitRule.TWO_HANDS,
        resplit_aces=False,
        surrender_rule=SurrenderRule.NO_SURRENDER,
        blackjack_payout=1.5,
        insurance_available=False
    ),
    
    BlackjackVariant.ATLANTIC_CITY: BlackjackRules(
        num_decks=8,
        dealer_rule=DealerRule.STAND_SOFT_17,
        double_rule=DoubleRule.ANY_TWO_CARDS,
        double_after_split=True,
        split_rule=SplitRule.THREE_HANDS,
        resplit_aces=True,
        surrender_rule=SurrenderRule.LATE,
        blackjack_payout=1.5,
        insurance_available=True
    ),
    
    BlackjackVariant.LAS_VEGAS: BlackjackRules(
        num_decks=6,
        dealer_rule=DealerRule.HIT_SOFT_17,
        double_rule=DoubleRule.ANY_TWO_CARDS,
        double_after_split=True,
        split_rule=SplitRule.THREE_HANDS,
        resplit_aces=False,
        surrender_rule=SurrenderRule.LATE,
        blackjack_payout=1.2,  # 6:5 payout (common in Vegas)
        insurance_available=True
    ),
    
    BlackjackVariant.ONLINE_LIVE: BlackjackRules(
        num_decks=6,
        continuous_shuffle=True,  # CSM simulation
        dealer_rule=DealerRule.HIT_SOFT_17,
        double_rule=DoubleRule.ANY_TWO_CARDS,
        double_after_split=True,
        split_rule=SplitRule.THREE_HANDS,
        resplit_aces=False,
        surrender_rule=SurrenderRule.LATE,
        blackjack_payout=1.5,
        insurance_available=True
    ),
    
    BlackjackVariant.SINGLE_DECK: BlackjackRules(
        num_decks=1,
        continuous_shuffle=False,
        deck_penetration=0.6,
        dealer_rule=DealerRule.STAND_SOFT_17,
        double_rule=DoubleRule.TEN_ELEVEN,
        double_after_split=False,
        split_rule=SplitRule.TWO_HANDS,
        resplit_aces=False,
        surrender_rule=SurrenderRule.NO_SURRENDER,
        blackjack_payout=1.5,
        insurance_available=True
    )
}


def get_rules(variant: BlackjackVariant) -> BlackjackRules:
    """Get predefined rules for a blackjack variant."""
    return RULE_PRESETS[variant]


def get_realistic_rules() -> BlackjackRules:
    """Get realistic online casino rules (default for training)."""
    return RULE_PRESETS[BlackjackVariant.ONLINE_LIVE]


def get_training_rules() -> BlackjackRules:
    """Get optimized rules for ML training."""
    rules = get_realistic_rules()
    rules.max_hands_per_episode = 1  # Single hand episodes for faster training
    rules.continuous_shuffle = True  # Consistent deck state
    return rules
