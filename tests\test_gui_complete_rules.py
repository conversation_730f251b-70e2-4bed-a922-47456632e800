"""
Tests for Complete GUI Rule Implementation.

This module tests the enhanced GUI components that support complete
blackjack rule implementation including insurance, surrender, splitting,
and side bets.
"""

import unittest
import tkinter as tk
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gui.game_actions import GameActionsWidget
from gui.multi_hand_display import MultiHandDisplayWidget
from core.card import Card, Rank, Suit
from core.hand import Hand
from core.game_logic import GameState, GameAction
from core.game_rules import BlackjackRules


class TestGameActionsWidget(unittest.TestCase):
    """Test the GameActionsWidget functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide window during testing
        
        self.on_action_taken = Mock()
        self.on_game_state_change = Mock()
        
        self.widget = GameActionsWidget(
            self.root,
            on_action_taken=self.on_action_taken,
            on_game_state_change=self.on_game_state_change
        )
        self.widget.setup_layout()
        
    def tearDown(self):
        """Clean up test environment."""
        self.root.destroy()
        
    def test_widget_initialization(self):
        """Test widget initializes correctly."""
        self.assertIsNotNone(self.widget.main_frame)
        self.assertIsNotNone(self.widget.action_buttons)
        self.assertEqual(self.widget.bankroll, 1000.0)
        self.assertEqual(self.widget.bet_amount, 10.0)
        
    def test_betting_controls(self):
        """Test betting controls functionality."""
        # Test bet amount change
        self.widget.bet_var.set(25.0)
        self.widget.on_bet_change()
        self.assertEqual(self.widget.bet_amount, 25.0)
        
        # Test bankroll display update
        self.widget.set_bankroll(500.0)
        self.assertEqual(self.widget.bankroll, 500.0)
        
    def test_side_bet_controls(self):
        """Test side bet controls."""
        # Enable Perfect Pairs
        self.widget.perfect_pairs_var.set(True)
        self.widget.on_side_bet_change()
        self.assertEqual(str(self.widget.perfect_pairs_spinbox['state']), 'normal')

        # Enable 21+3
        self.widget.twentyone_plus_three_var.set(True)
        self.widget.on_side_bet_change()
        self.assertEqual(str(self.widget.twentyone_plus_three_spinbox['state']), 'normal')

        # Disable side bets
        self.widget.perfect_pairs_var.set(False)
        self.widget.twentyone_plus_three_var.set(False)
        self.widget.on_side_bet_change()
        self.assertEqual(str(self.widget.perfect_pairs_spinbox['state']), 'disabled')
        self.assertEqual(str(self.widget.twentyone_plus_three_spinbox['state']), 'disabled')
        
    def test_insurance_functionality(self):
        """Test insurance betting functionality."""
        # Test insurance enable/disable
        self.widget.enable_insurance()
        self.assertEqual(str(self.widget.insurance_spinbox['state']), 'normal')
        self.assertEqual(self.widget.insurance_var.get(), 5.0)  # Half of default bet

        self.widget.disable_insurance()
        self.assertEqual(str(self.widget.insurance_spinbox['state']), 'disabled')
        self.assertEqual(self.widget.insurance_var.get(), 0.0)
        
    def test_new_game_start(self):
        """Test starting a new game."""
        initial_bankroll = self.widget.bankroll
        bet_amount = self.widget.bet_amount
        
        # Mock game start
        with patch.object(self.widget.game, 'start_new_game') as mock_start:
            mock_game_state = Mock()
            mock_game_state.dealer_hand.cards = [Card(Suit.SPADES, Rank.ACE)]
            mock_game_state.player_hands = []
            mock_game_state.current_hand_index = 0
            mock_start.return_value = mock_game_state
            
            self.widget.start_new_game()
            
            # Check bet was deducted
            self.assertEqual(self.widget.bankroll, initial_bankroll - bet_amount)
            
            # Check game was started
            mock_start.assert_called_once_with(bet_amount=bet_amount)
            
            # Check callback was called
            self.on_game_state_change.assert_called_once_with(mock_game_state)
            
    def test_action_taking(self):
        """Test taking game actions."""
        # Set up game state
        mock_game_state = Mock()
        self.widget.current_game_state = mock_game_state
        
        # Mock available actions
        with patch.object(self.widget.game, 'get_available_actions') as mock_actions:
            mock_actions.return_value = [GameAction.HIT, GameAction.STAND]
            
            with patch.object(self.widget.game, 'take_action') as mock_take_action:
                mock_take_action.return_value = mock_game_state
                
                # Test taking hit action
                self.widget.take_action(GameAction.HIT)
                
                mock_take_action.assert_called_once_with(GameAction.HIT)
                self.on_action_taken.assert_called_once_with(GameAction.HIT, mock_game_state)
                
    def test_side_bet_payouts(self):
        """Test side bet payout calculations."""
        # Test Perfect Pairs
        hand = Hand()
        hand.add_card(Card(Suit.HEARTS, Rank.KING))
        hand.add_card(Card(Suit.SPADES, Rank.KING))

        self.assertTrue(self.widget.check_perfect_pairs(hand))

        # Test 21+3
        dealer_upcard = Card(Suit.HEARTS, Rank.QUEEN)
        self.assertTrue(self.widget.check_twentyone_plus_three(hand, dealer_upcard))
        
    def test_bankroll_management(self):
        """Test bankroll management features."""
        # Test affordability checks
        self.widget.bankroll = 50.0
        self.widget.bet_amount = 25.0
        
        self.assertTrue(self.widget.can_afford_double())
        self.assertTrue(self.widget.can_afford_split())
        
        self.widget.bet_amount = 60.0
        self.assertFalse(self.widget.can_afford_double())
        self.assertFalse(self.widget.can_afford_split())


class TestMultiHandDisplayWidget(unittest.TestCase):
    """Test the MultiHandDisplayWidget functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide window during testing
        
        self.widget = MultiHandDisplayWidget(self.root)
        self.widget.setup_layout()
        
    def tearDown(self):
        """Clean up test environment."""
        self.root.destroy()
        
    def test_widget_initialization(self):
        """Test widget initializes correctly."""
        self.assertIsNotNone(self.widget.main_frame)
        self.assertIsNotNone(self.widget.dealer_frame)
        self.assertIsNotNone(self.widget.player_frame)
        self.assertIsNotNone(self.widget.betting_frame)
        
    def test_dealer_display_update(self):
        """Test dealer hand display updates."""
        # Create test game state
        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.SPADES, Rank.ACE))
        dealer_hand.add_card(Card(Suit.HEARTS, Rank.KING))
        
        game_state = Mock()
        game_state.dealer_hand = dealer_hand
        game_state.player_hands = []
        game_state.current_hand_index = 0
        
        self.widget.game_state = game_state
        self.widget.update_dealer_display()
        
        # Check dealer value was updated
        self.assertIn("21", self.widget.dealer_value_label['text'])
        
    def test_multi_hand_display(self):
        """Test multiple player hands display."""
        # Create test hands
        hand1 = Hand()
        hand1.add_card(Card(Suit.HEARTS, Rank.TEN))
        hand1.add_card(Card(Suit.SPADES, Rank.NINE))

        hand2 = Hand()
        hand2.add_card(Card(Suit.CLUBS, Rank.ACE))
        hand2.add_card(Card(Suit.DIAMONDS, Rank.KING))

        dealer_hand = Hand()
        dealer_hand.add_card(Card(Suit.HEARTS, Rank.SEVEN))
        
        # Create game state
        game_state = Mock()
        game_state.dealer_hand = dealer_hand
        game_state.player_hands = [hand1, hand2]
        game_state.current_hand_index = 0
        
        bet_amounts = {0: 10.0, 1: 10.0}
        
        self.widget.update_game_state(game_state, bet_amounts)
        
        # Check that both hands are displayed
        self.assertEqual(len(self.widget.hand_frames), 2)
        self.assertIn(0, self.widget.hand_frames)
        self.assertIn(1, self.widget.hand_frames)
        
    def test_betting_display_update(self):
        """Test betting information display."""
        bet_amounts = {0: 25.0, 1: 25.0}
        insurance_bet = 12.5
        side_bets = {'perfect_pairs': 5.0, '21+3': 5.0}
        
        self.widget.bet_amounts = bet_amounts
        self.widget.insurance_bet = insurance_bet
        self.widget.side_bets = side_bets
        
        self.widget.update_betting_display()
        
        # Check displays were updated (basic check)
        self.assertIsNotNone(self.widget.insurance_label['text'])
        self.assertIsNotNone(self.widget.side_bets_label['text'])
        
    def test_card_display_creation(self):
        """Test card label creation."""
        card = Card(Suit.HEARTS, Rank.ACE)
        card_label = self.widget.create_card_label(self.widget.main_frame, card)

        self.assertIsNotNone(card_label)
        self.assertIn("ACE", card_label['text'])
        self.assertIn("♥", card_label['text'])
        
    def test_hand_status_text(self):
        """Test hand status text generation."""
        # Test blackjack
        blackjack_hand = Hand()
        blackjack_hand.add_card(Card(Suit.HEARTS, Rank.ACE))
        blackjack_hand.add_card(Card(Suit.SPADES, Rank.KING))

        self.assertEqual(self.widget.get_hand_status_text(blackjack_hand), "Blackjack!")

        # Test bust
        bust_hand = Hand()
        bust_hand.add_card(Card(Suit.HEARTS, Rank.KING))
        bust_hand.add_card(Card(Suit.SPADES, Rank.QUEEN))
        bust_hand.add_card(Card(Suit.CLUBS, Rank.JACK))

        self.assertEqual(self.widget.get_hand_status_text(bust_hand), "Bust")

        # Test 21
        twenty_one_hand = Hand()
        twenty_one_hand.add_card(Card(Suit.HEARTS, Rank.SEVEN))
        twenty_one_hand.add_card(Card(Suit.SPADES, Rank.SEVEN))
        twenty_one_hand.add_card(Card(Suit.CLUBS, Rank.SEVEN))
        
        self.assertEqual(self.widget.get_hand_status_text(twenty_one_hand), "21")
        
    def test_clear_display(self):
        """Test clearing the display."""
        # Set up some state
        self.widget.game_state = Mock()
        self.widget.bet_amounts = {0: 10.0}
        self.widget.insurance_bet = 5.0
        self.widget.side_bets = {'perfect_pairs': 2.0}
        
        # Clear display
        self.widget.clear_display()
        
        # Check state was cleared
        self.assertIsNone(self.widget.game_state)
        self.assertEqual(self.widget.bet_amounts, {})
        self.assertEqual(self.widget.insurance_bet, 0.0)
        self.assertEqual(self.widget.side_bets, {})


class TestGUIIntegration(unittest.TestCase):
    """Test integration between GUI components."""
    
    def setUp(self):
        """Set up test environment."""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide window during testing
        
    def tearDown(self):
        """Clean up test environment."""
        self.root.destroy()
        
    def test_action_widget_display_integration(self):
        """Test integration between action widget and display widget."""
        # Create widgets
        display_widget = MultiHandDisplayWidget(self.root)
        action_widget = GameActionsWidget(self.root)
        
        # Set up layouts
        display_widget.setup_layout()
        action_widget.setup_layout()
        
        # Test that they can work together
        self.assertIsNotNone(display_widget)
        self.assertIsNotNone(action_widget)
        
        # Test state synchronization
        action_widget.bet_amount = 25.0
        action_widget.insurance_bet = 12.5
        action_widget.side_bets = {'perfect_pairs': 5.0}
        
        # Create mock game state
        game_state = Mock()
        game_state.player_hands = [Hand()]
        game_state.dealer_hand = Hand()
        game_state.current_hand_index = 0
        
        # Update display with action widget state
        bet_amounts = {0: action_widget.bet_amount}
        display_widget.update_game_state(
            game_state=game_state,
            bet_amounts=bet_amounts,
            insurance_bet=action_widget.insurance_bet,
            side_bets=action_widget.side_bets
        )
        
        # Verify integration worked
        self.assertEqual(display_widget.bet_amounts, bet_amounts)
        self.assertEqual(display_widget.insurance_bet, action_widget.insurance_bet)
        self.assertEqual(display_widget.side_bets, action_widget.side_bets)


if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
