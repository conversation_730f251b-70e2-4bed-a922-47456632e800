"""
Multi-Hand Display Widget for BlackJack Bot ML GUI.

This module implements an enhanced game display that can handle multiple
player hands from splitting, with clear visual separation and status indicators.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.card import Card, Suit
from core.hand import Hand
from core.game_logic import GameState


class MultiHandDisplayWidget:
    """
    Enhanced game display widget for multiple hands.
    
    Displays dealer hand and multiple player hands with:
    - Clear visual separation between hands
    - Current hand highlighting
    - Hand status indicators (blackjack, bust, etc.)
    - Bet amounts for each hand
    - Insurance and side bet displays
    """
    
    def __init__(self, parent):
        """
        Initialize the multi-hand display widget.
        
        Args:
            parent: Parent tkinter widget
        """
        self.parent = parent
        
        # Current game state
        self.game_state = None
        self.bet_amounts = {}
        self.insurance_bet = 0.0
        self.side_bets = {}
        
        # Suit symbols for display
        self.suit_symbols = {
            Suit.HEARTS: "♥",
            Suit.DIAMONDS: "♦", 
            Suit.CLUBS: "♣",
            Suit.SPADES: "♠"
        }
        
        # Colors for suits
        self.suit_colors = {
            Suit.HEARTS: "red",
            Suit.DIAMONDS: "red",
            Suit.CLUBS: "black", 
            Suit.SPADES: "black"
        }
        
        # Hand display widgets
        self.hand_frames = {}
        self.hand_labels = {}
        self.card_displays = {}
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create all display widgets."""
        # Main container with scrollable frame for multiple hands
        self.main_frame = ttk.Frame(self.parent)
        
        # Title
        self.title_label = ttk.Label(
            self.main_frame,
            text="Game Display",
            font=('Arial', 14, 'bold')
        )
        
        # Dealer section
        self.create_dealer_section()
        
        # Player hands section (scrollable)
        self.create_player_section()
        
        # Betting information section
        self.create_betting_section()
        
    def create_dealer_section(self):
        """Create dealer hand display section."""
        self.dealer_frame = ttk.LabelFrame(
            self.main_frame,
            text="Dealer",
            padding="10"
        )
        
        # Dealer cards display
        self.dealer_cards_frame = ttk.Frame(self.dealer_frame)
        
        # Dealer hand value
        self.dealer_value_label = ttk.Label(
            self.dealer_frame,
            text="Value: --",
            font=('Arial', 12, 'bold')
        )
        
        # Dealer status
        self.dealer_status_label = ttk.Label(
            self.dealer_frame,
            text="",
            font=('Arial', 10)
        )
        
    def create_player_section(self):
        """Create player hands display section."""
        self.player_frame = ttk.LabelFrame(
            self.main_frame,
            text="Player Hands",
            padding="10"
        )
        
        # Scrollable frame for multiple hands
        self.player_canvas = tk.Canvas(self.player_frame, height=200)
        self.player_scrollbar = ttk.Scrollbar(
            self.player_frame,
            orient="vertical",
            command=self.player_canvas.yview
        )
        self.player_canvas.configure(yscrollcommand=self.player_scrollbar.set)
        
        # Scrollable content frame
        self.player_content_frame = ttk.Frame(self.player_canvas)
        self.player_canvas.create_window((0, 0), window=self.player_content_frame, anchor="nw")
        
        # Bind scroll events
        self.player_content_frame.bind("<Configure>", self.on_player_frame_configure)
        self.player_canvas.bind("<MouseWheel>", self.on_mousewheel)
        
    def create_betting_section(self):
        """Create betting information display."""
        self.betting_frame = ttk.LabelFrame(
            self.main_frame,
            text="Betting Information",
            padding="10"
        )
        
        # Main bets display
        self.main_bets_frame = ttk.Frame(self.betting_frame)
        
        # Insurance display
        self.insurance_frame = ttk.Frame(self.betting_frame)
        self.insurance_label = ttk.Label(
            self.insurance_frame,
            text="Insurance: $0.00",
            font=('Arial', 10)
        )
        
        # Side bets display
        self.side_bets_frame = ttk.Frame(self.betting_frame)
        self.side_bets_label = ttk.Label(
            self.side_bets_frame,
            text="Side Bets: None",
            font=('Arial', 10)
        )
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew', padx=5, pady=5)
        
        # Configure grid weights
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=0)  # Dealer
        self.main_frame.grid_rowconfigure(2, weight=1)  # Player hands
        self.main_frame.grid_rowconfigure(3, weight=0)  # Betting info
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        self.title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Dealer section
        self.dealer_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
        self.dealer_cards_frame.grid(row=0, column=0, sticky='ew')
        self.dealer_value_label.grid(row=1, column=0, pady=(5, 0))
        self.dealer_status_label.grid(row=2, column=0)
        
        # Player section
        self.player_frame.grid(row=2, column=0, sticky='nsew', pady=(0, 10))
        self.player_canvas.grid(row=0, column=0, sticky='nsew')
        self.player_scrollbar.grid(row=0, column=1, sticky='ns')
        
        # Configure player frame grid
        self.player_frame.grid_rowconfigure(0, weight=1)
        self.player_frame.grid_columnconfigure(0, weight=1)
        
        # Betting section
        self.betting_frame.grid(row=3, column=0, sticky='ew')
        self.main_bets_frame.grid(row=0, column=0, sticky='ew')
        self.insurance_frame.grid(row=1, column=0, sticky='ew', pady=(5, 0))
        self.side_bets_frame.grid(row=2, column=0, sticky='ew', pady=(5, 0))
        
        self.insurance_label.grid(row=0, column=0, sticky='w')
        self.side_bets_label.grid(row=0, column=0, sticky='w')
        
    def update_game_state(self, game_state: GameState, bet_amounts: Dict[int, float] = None,
                         insurance_bet: float = 0.0, side_bets: Dict[str, float] = None):
        """
        Update the display with new game state.
        
        Args:
            game_state: Current game state
            bet_amounts: Bet amounts for each hand
            insurance_bet: Insurance bet amount
            side_bets: Side bet amounts
        """
        self.game_state = game_state
        self.bet_amounts = bet_amounts or {}
        self.insurance_bet = insurance_bet
        self.side_bets = side_bets or {}
        
        self.update_dealer_display()
        self.update_player_displays()
        self.update_betting_display()
        
    def update_dealer_display(self):
        """Update dealer hand display."""
        if not self.game_state:
            return
            
        # Clear existing cards
        for widget in self.dealer_cards_frame.winfo_children():
            widget.destroy()
            
        # Display dealer cards
        dealer_hand = self.game_state.dealer_hand
        for i, card in enumerate(dealer_hand.cards):
            card_label = self.create_card_label(self.dealer_cards_frame, card)
            card_label.grid(row=0, column=i, padx=2)
            
        # Update dealer value
        dealer_value = dealer_hand.get_value()
        self.dealer_value_label.configure(text=f"Value: {dealer_value}")
        
        # Update dealer status
        status_text = ""
        if dealer_hand.is_blackjack():
            status_text = "Blackjack!"
        elif dealer_hand.is_bust():
            status_text = "Bust"
        elif len(dealer_hand.cards) == 1:
            status_text = "Hidden card"
            
        self.dealer_status_label.configure(text=status_text)
        
    def update_player_displays(self):
        """Update all player hand displays."""
        if not self.game_state:
            return
            
        # Clear existing hand displays
        for widget in self.player_content_frame.winfo_children():
            widget.destroy()
            
        self.hand_frames = {}
        self.hand_labels = {}
        self.card_displays = {}
        
        # Create display for each player hand
        for i, hand in enumerate(self.game_state.player_hands):
            self.create_hand_display(i, hand)
            
        # Update scroll region
        self.player_content_frame.update_idletasks()
        self.player_canvas.configure(scrollregion=self.player_canvas.bbox("all"))
        
    def create_hand_display(self, hand_index: int, hand: Hand):
        """Create display for a single hand."""
        # Hand frame
        is_current = hand_index == self.game_state.current_hand_index

        hand_frame = ttk.LabelFrame(
            self.player_content_frame,
            text=f"Hand {hand_index + 1}" + (" (CURRENT)" if is_current else ""),
            padding="10"
        )
        hand_frame.grid(row=hand_index, column=0, sticky='ew', pady=2)
        
        # Configure grid weight
        self.player_content_frame.grid_columnconfigure(0, weight=1)
        
        # Cards display
        cards_frame = ttk.Frame(hand_frame)
        cards_frame.grid(row=0, column=0, sticky='ew')
        
        for j, card in enumerate(hand.cards):
            card_label = self.create_card_label(cards_frame, card)
            card_label.grid(row=0, column=j, padx=2)
            
        # Hand information
        info_frame = ttk.Frame(hand_frame)
        info_frame.grid(row=1, column=0, sticky='ew', pady=(5, 0))
        
        # Hand value
        value_label = ttk.Label(
            info_frame,
            text=f"Value: {hand.get_value()}",
            font=('Arial', 11, 'bold')
        )
        value_label.grid(row=0, column=0, sticky='w')
        
        # Hand status
        status_text = self.get_hand_status_text(hand)
        status_label = ttk.Label(
            info_frame,
            text=status_text,
            font=('Arial', 10)
        )
        status_label.grid(row=0, column=1, padx=(20, 0), sticky='w')
        
        # Bet amount
        bet_amount = self.bet_amounts.get(hand_index, 0.0)
        bet_label = ttk.Label(
            info_frame,
            text=f"Bet: ${bet_amount:.2f}",
            font=('Arial', 10)
        )
        bet_label.grid(row=0, column=2, padx=(20, 0), sticky='w')
        
        # Current hand indicator
        if is_current:
            current_label = ttk.Label(
                info_frame,
                text="← CURRENT",
                font=('Arial', 10, 'bold'),
                foreground='blue'
            )
            current_label.grid(row=0, column=3, padx=(20, 0), sticky='w')
            
        # Store references
        self.hand_frames[hand_index] = hand_frame
        self.hand_labels[hand_index] = {
            'value': value_label,
            'status': status_label,
            'bet': bet_label
        }
        self.card_displays[hand_index] = cards_frame
        
    def create_card_label(self, parent, card: Card) -> ttk.Label:
        """Create a label for displaying a card."""
        suit_symbol = self.suit_symbols.get(card.suit, "?")
        suit_color = self.suit_colors.get(card.suit, "black")
        
        # Format card text
        rank_text = card.rank.symbol  # Use the symbol property from Rank enum
        card_text = f"{rank_text}\n{suit_symbol}"
        
        # Create label with card styling
        card_label = ttk.Label(
            parent,
            text=card_text,
            font=('Arial', 10, 'bold'),
            foreground=suit_color,
            background='white',
            relief='solid',
            borderwidth=1,
            width=4,
            anchor='center'
        )
        
        return card_label
        
    def get_hand_status_text(self, hand: Hand) -> str:
        """Get status text for a hand."""
        if hand.is_blackjack():
            return "Blackjack!"
        elif hand.is_bust():
            return "Bust"
        elif hand.get_value() == 21:
            return "21"
        else:
            return ""
            
    def update_betting_display(self):
        """Update betting information display."""
        # Clear existing bet displays
        for widget in self.main_bets_frame.winfo_children():
            widget.destroy()
            
        # Display main bets
        total_main_bets = sum(self.bet_amounts.values())
        main_bets_label = ttk.Label(
            self.main_bets_frame,
            text=f"Main Bets: ${total_main_bets:.2f}",
            font=('Arial', 11, 'bold')
        )
        main_bets_label.grid(row=0, column=0, sticky='w')
        
        # Display individual hand bets if multiple hands
        if len(self.bet_amounts) > 1:
            for i, (hand_index, amount) in enumerate(self.bet_amounts.items()):
                bet_label = ttk.Label(
                    self.main_bets_frame,
                    text=f"Hand {hand_index + 1}: ${amount:.2f}",
                    font=('Arial', 9)
                )
                bet_label.grid(row=1, column=i, padx=(0, 10), sticky='w')
                
        # Update insurance display
        insurance_text = f"Insurance: ${self.insurance_bet:.2f}" if self.insurance_bet > 0 else "Insurance: None"
        self.insurance_label.configure(text=insurance_text)
        
        # Update side bets display
        if self.side_bets:
            side_bets_text = ", ".join([f"{name}: ${amount:.2f}" for name, amount in self.side_bets.items()])
            side_bets_text = f"Side Bets: {side_bets_text}"
        else:
            side_bets_text = "Side Bets: None"
        self.side_bets_label.configure(text=side_bets_text)
        
    def on_player_frame_configure(self, event):
        """Handle player frame configuration changes."""
        self.player_canvas.configure(scrollregion=self.player_canvas.bbox("all"))
        
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.player_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
    def clear_display(self):
        """Clear all displays."""
        self.game_state = None
        self.bet_amounts = {}
        self.insurance_bet = 0.0
        self.side_bets = {}
        
        # Clear dealer display
        for widget in self.dealer_cards_frame.winfo_children():
            widget.destroy()
        self.dealer_value_label.configure(text="Value: --")
        self.dealer_status_label.configure(text="")
        
        # Clear player displays
        for widget in self.player_content_frame.winfo_children():
            widget.destroy()
            
        # Clear betting display
        for widget in self.main_bets_frame.winfo_children():
            widget.destroy()
        self.insurance_label.configure(text="Insurance: $0.00")
        self.side_bets_label.configure(text="Side Bets: None")
