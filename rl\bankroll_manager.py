"""
Bankroll Management System for Blackjack RL Agent.

This module implements comprehensive bankroll management including:
- Kelly Criterion betting strategy
- Dynamic bet sizing based on bankroll and confidence
- Risk of ruin calculations
- Long-term growth optimization
"""

import numpy as np
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class BettingStrategy(Enum):
    """Available betting strategies."""
    FLAT = "flat"
    KELLY = "kelly"
    PROPORTIONAL = "proportional"
    CONSERVATIVE = "conservative"
    AGGRESSIVE = "aggressive"


@dataclass
class BankrollConfig:
    """Configuration for bankroll management."""
    
    # Initial bankroll settings
    initial_bankroll: float = 1000.0
    base_bet_size: float = 10.0
    min_bet_size: float = 5.0
    max_bet_size: float = 500.0
    
    # Betting strategy
    betting_strategy: BettingStrategy = BettingStrategy.KELLY
    kelly_fraction: float = 0.25  # <PERSON> (25% of optimal)
    
    # Risk management
    max_bet_percentage: float = 0.05  # Max 5% of bankroll per bet
    stop_loss_percentage: float = 0.5  # Stop at 50% bankroll loss
    take_profit_percentage: float = 2.0  # Take profit at 200% bankroll
    
    # Confidence-based adjustments
    confidence_threshold: float = 0.6  # Minimum confidence for increased bets
    confidence_multiplier: float = 1.5  # Multiplier for high-confidence bets
    
    # Risk of ruin parameters
    target_ruin_probability: float = 0.01  # Target 1% risk of ruin
    
    # Session management
    session_target: float = 0.2  # Target 20% gain per session
    session_stop_loss: float = 0.3  # Stop session at 30% loss


@dataclass
class BankrollMetrics:
    """Metrics for bankroll performance tracking."""
    
    # Current state
    current_bankroll: float = 0.0
    current_bet_size: float = 0.0
    total_hands_played: int = 0
    
    # Performance tracking
    total_wagered: float = 0.0
    total_won: float = 0.0
    total_lost: float = 0.0
    net_profit: float = 0.0
    
    # Growth metrics
    bankroll_history: List[float] = field(default_factory=list)
    bet_history: List[float] = field(default_factory=list)
    roi: float = 0.0  # Return on investment
    
    # Risk metrics
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    risk_of_ruin: float = 0.0
    
    # Session tracking
    session_profit: float = 0.0
    session_hands: int = 0
    sessions_played: int = 0
    
    # Betting strategy performance
    kelly_optimal_bets: int = 0
    confidence_adjusted_bets: int = 0
    conservative_bets: int = 0


class BankrollManager:
    """
    Comprehensive bankroll management system.
    
    Manages betting strategy, risk assessment, and long-term growth optimization
    for the blackjack RL agent.
    """
    
    def __init__(self, config: BankrollConfig):
        """Initialize bankroll manager."""
        self.config = config
        self.metrics = BankrollMetrics()
        self.metrics.current_bankroll = config.initial_bankroll
        self.metrics.current_bet_size = config.base_bet_size
        
        # Performance tracking
        self.win_rate_history = []
        self.edge_estimates = []
        self.variance_estimates = []
        
        # Session state
        self.session_start_bankroll = config.initial_bankroll
        self.session_active = False
        
    def start_session(self) -> None:
        """Start a new betting session."""
        self.session_start_bankroll = self.metrics.current_bankroll
        self.metrics.session_profit = 0.0
        self.metrics.session_hands = 0
        self.session_active = True
        
        logger.info(f"Started new session with bankroll: ${self.metrics.current_bankroll:.2f}")
        
    def end_session(self) -> Dict[str, Any]:
        """End current session and return summary."""
        if not self.session_active:
            return {}
        
        session_summary = {
            "session_profit": self.metrics.session_profit,
            "session_hands": self.metrics.session_hands,
            "session_roi": self.metrics.session_profit / self.session_start_bankroll,
            "final_bankroll": self.metrics.current_bankroll,
            "bankroll_change": self.metrics.current_bankroll - self.session_start_bankroll
        }
        
        self.metrics.sessions_played += 1
        self.session_active = False
        
        logger.info(f"Session ended: {session_summary}")
        return session_summary
        
    def calculate_bet_size(self, model_confidence: float = 0.5, 
                          estimated_edge: float = 0.0) -> float:
        """
        Calculate optimal bet size based on bankroll, strategy, and confidence.
        
        Args:
            model_confidence: ML model confidence (0-1)
            estimated_edge: Estimated edge for this hand (-1 to 1)
            
        Returns:
            Optimal bet size
        """
        if self.metrics.current_bankroll <= 0:
            return 0.0
        
        base_bet = self._calculate_base_bet()
        
        if self.config.betting_strategy == BettingStrategy.FLAT:
            bet_size = self.config.base_bet_size
        elif self.config.betting_strategy == BettingStrategy.KELLY:
            bet_size = self._calculate_kelly_bet(estimated_edge)
        elif self.config.betting_strategy == BettingStrategy.PROPORTIONAL:
            bet_size = self.metrics.current_bankroll * self.config.max_bet_percentage
        elif self.config.betting_strategy == BettingStrategy.CONSERVATIVE:
            bet_size = min(base_bet, self.metrics.current_bankroll * 0.02)
        else:  # AGGRESSIVE
            bet_size = min(base_bet * 2, self.metrics.current_bankroll * 0.1)
        
        # Apply confidence adjustments
        if model_confidence > self.config.confidence_threshold:
            bet_size *= self.config.confidence_multiplier
            self.metrics.confidence_adjusted_bets += 1
        
        # Apply constraints
        bet_size = self._apply_bet_constraints(bet_size)
        
        self.metrics.current_bet_size = bet_size
        self.metrics.bet_history.append(bet_size)
        
        return bet_size
        
    def _calculate_base_bet(self) -> float:
        """Calculate base bet size as percentage of current bankroll."""
        return self.metrics.current_bankroll * (self.config.base_bet_size / self.config.initial_bankroll)
        
    def _calculate_kelly_bet(self, estimated_edge: float) -> float:
        """Calculate Kelly Criterion bet size."""
        if estimated_edge <= 0:
            return self.config.min_bet_size
        
        # Estimate variance from historical data
        if len(self.variance_estimates) > 0:
            variance = np.mean(self.variance_estimates[-20:])  # Last 20 estimates
        else:
            variance = 1.0  # Default variance for blackjack
        
        # Kelly formula: f = (bp - q) / b
        # where b = odds, p = win probability, q = loss probability
        win_prob = 0.5 + (estimated_edge / 2)  # Convert edge to win probability
        loss_prob = 1 - win_prob
        odds = 1.0  # Even money for simplicity
        
        kelly_fraction = (odds * win_prob - loss_prob) / odds
        
        # Apply fractional Kelly for risk management
        kelly_fraction *= self.config.kelly_fraction
        
        bet_size = self.metrics.current_bankroll * kelly_fraction
        
        if kelly_fraction > 0:
            self.metrics.kelly_optimal_bets += 1
        
        return max(bet_size, self.config.min_bet_size)
        
    def _apply_bet_constraints(self, bet_size: float) -> float:
        """Apply betting constraints and limits."""
        # Minimum bet
        bet_size = max(bet_size, self.config.min_bet_size)
        
        # Maximum bet (absolute)
        bet_size = min(bet_size, self.config.max_bet_size)
        
        # Maximum bet (percentage of bankroll)
        max_percentage_bet = self.metrics.current_bankroll * self.config.max_bet_percentage
        bet_size = min(bet_size, max_percentage_bet)
        
        # Ensure we don't bet more than we have
        bet_size = min(bet_size, self.metrics.current_bankroll)
        
        return bet_size
        
    def update_bankroll(self, bet_amount: float, result: str, 
                       payout_multiplier: float = 1.0) -> Dict[str, Any]:
        """
        Update bankroll based on hand result.
        
        Args:
            bet_amount: Amount wagered
            result: 'win', 'loss', 'push', 'blackjack'
            payout_multiplier: Payout multiplier (1.5 for blackjack, 1.0 for regular win)
            
        Returns:
            Updated bankroll metrics
        """
        self.metrics.total_hands_played += 1
        self.metrics.session_hands += 1
        self.metrics.total_wagered += bet_amount
        
        if result == 'win' or result == 'blackjack':
            winnings = bet_amount * payout_multiplier
            self.metrics.current_bankroll += winnings
            self.metrics.total_won += winnings
            self.metrics.session_profit += winnings
        elif result == 'loss':
            self.metrics.current_bankroll -= bet_amount
            self.metrics.total_lost += bet_amount
            self.metrics.session_profit -= bet_amount
        # 'push' - no change to bankroll
        
        # Update metrics
        self._update_performance_metrics()
        
        # Check session limits
        session_status = self._check_session_limits()
        
        return {
            "current_bankroll": self.metrics.current_bankroll,
            "session_profit": self.metrics.session_profit,
            "total_hands": self.metrics.total_hands_played,
            "roi": self.metrics.roi,
            "risk_of_ruin": self.metrics.risk_of_ruin,
            "session_status": session_status
        }
        
    def _update_performance_metrics(self) -> None:
        """Update performance and risk metrics."""
        # Update bankroll history
        self.metrics.bankroll_history.append(self.metrics.current_bankroll)
        
        # Calculate ROI
        if self.config.initial_bankroll > 0:
            self.metrics.roi = (self.metrics.current_bankroll - self.config.initial_bankroll) / self.config.initial_bankroll
        
        # Calculate net profit
        self.metrics.net_profit = self.metrics.total_won - self.metrics.total_lost
        
        # Update drawdown metrics
        if len(self.metrics.bankroll_history) > 1:
            peak_bankroll = max(self.metrics.bankroll_history)
            current_drawdown = (peak_bankroll - self.metrics.current_bankroll) / peak_bankroll
            self.metrics.current_drawdown = current_drawdown
            self.metrics.max_drawdown = max(self.metrics.max_drawdown, current_drawdown)
        
        # Estimate risk of ruin
        self.metrics.risk_of_ruin = self._calculate_risk_of_ruin()
        
    def _calculate_risk_of_ruin(self) -> float:
        """Calculate risk of ruin based on current performance."""
        if len(self.metrics.bankroll_history) < 10:
            return 0.5  # High uncertainty with limited data
        
        # Estimate win rate and edge from recent performance
        recent_hands = min(100, self.metrics.total_hands_played)
        if recent_hands == 0:
            return 0.5
        
        # Simple risk of ruin approximation
        # ROR ≈ ((1-p)/p)^(bankroll/bet_size) where p is win probability
        avg_bet = np.mean(self.metrics.bet_history[-recent_hands:]) if self.metrics.bet_history else self.config.base_bet_size
        
        if avg_bet <= 0:
            return 0.0
        
        # Estimate win probability from recent performance
        win_rate = max(0.01, min(0.99, self.metrics.roi + 0.5))  # Rough approximation
        
        # Risk of ruin formula (simplified)
        if win_rate >= 0.5:
            ruin_prob = ((1 - win_rate) / win_rate) ** (self.metrics.current_bankroll / avg_bet)
        else:
            ruin_prob = 1.0 - (win_rate / (1 - win_rate)) ** (self.metrics.current_bankroll / avg_bet)
        
        return min(1.0, max(0.0, ruin_prob))
        
    def _check_session_limits(self) -> str:
        """Check if session limits have been reached."""
        if not self.session_active:
            return "inactive"
        
        session_roi = self.metrics.session_profit / self.session_start_bankroll
        
        # Check take profit
        if session_roi >= self.config.session_target:
            return "take_profit"
        
        # Check stop loss
        if session_roi <= -self.config.session_stop_loss:
            return "stop_loss"
        
        # Check overall bankroll limits
        overall_roi = (self.metrics.current_bankroll - self.config.initial_bankroll) / self.config.initial_bankroll
        
        if overall_roi <= -self.config.stop_loss_percentage:
            return "bankroll_stop_loss"
        
        if overall_roi >= self.config.take_profit_percentage:
            return "bankroll_take_profit"
        
        return "active"
        
    def get_bankroll_summary(self) -> Dict[str, Any]:
        """Get comprehensive bankroll summary."""
        return {
            "current_bankroll": self.metrics.current_bankroll,
            "initial_bankroll": self.config.initial_bankroll,
            "net_profit": self.metrics.net_profit,
            "roi": self.metrics.roi,
            "total_hands": self.metrics.total_hands_played,
            "total_wagered": self.metrics.total_wagered,
            "avg_bet_size": np.mean(self.metrics.bet_history) if self.metrics.bet_history else 0,
            "max_drawdown": self.metrics.max_drawdown,
            "current_drawdown": self.metrics.current_drawdown,
            "risk_of_ruin": self.metrics.risk_of_ruin,
            "session_profit": self.metrics.session_profit,
            "session_hands": self.metrics.session_hands,
            "sessions_played": self.metrics.sessions_played,
            "kelly_bets": self.metrics.kelly_optimal_bets,
            "confidence_bets": self.metrics.confidence_adjusted_bets
        }
        
    def reset_bankroll(self, new_bankroll: Optional[float] = None) -> None:
        """Reset bankroll to initial or specified amount."""
        if new_bankroll is not None:
            self.config.initial_bankroll = new_bankroll
        
        self.metrics = BankrollMetrics()
        self.metrics.current_bankroll = self.config.initial_bankroll
        self.metrics.current_bet_size = self.config.base_bet_size
        
        self.win_rate_history = []
        self.edge_estimates = []
        self.variance_estimates = []
        
        logger.info(f"Bankroll reset to ${self.metrics.current_bankroll:.2f}")
