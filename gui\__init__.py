"""
GUI Package for BlackJack Bot ML - Phase 4 Implementation.

This package provides a comprehensive graphical user interface for the BlackJack Bot ML system,
allowing users to interact with all agent types through an intuitive card selection interface.

Components:
- main_window: Primary GUI application window
- card_selector: Button-based card selection interface  
- advice_engine: ML agent integration and advice display
- game_display: Visual game state representation
- config_panel: Settings and configuration management

Author: Augment Agent
Phase: 4 (GUI Interface)
"""

__version__ = "1.0.0"
__author__ = "Augment Agent"

# Import main GUI components
from .main_window import BlackjackGUI
from .advice_engine import AdviceEngineGUI
from .card_selector import CardSelectorWidget
from .game_display import GameDisplayWidget
from .config_panel import ConfigPanelWidget

__all__ = [
    'BlackjackGUI',
    'AdviceEngineGUI', 
    'CardSelectorWidget',
    'GameDisplayWidget',
    'ConfigPanelWidget'
]
