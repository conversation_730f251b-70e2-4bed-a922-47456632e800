#!/usr/bin/env python3
"""
Minimal test to verify Phase 3 training pipeline can run one episode.
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_single_episode():
    """Test running a single training episode."""
    print("🧪 Testing single episode execution...")
    
    try:
        from rl.rl_environment import BlackjackRLEnvironment
        from rl.evasive_dqn_agent import EvasiveDQNAgent
        from rl.dqn_agent import DQNConfig
        from rl.evasion_strategies import EvasionConfig, EvasionTechnique
        from rl.adaptive_learning import AdaptationConfig
        from personas.cautious_persona import Cautious<PERSON>ersona
        from core.game_logic import GameAction
        
        print("   ✅ Imports successful")
        
        # Create minimal configurations
        dqn_config = DQNConfig(
            hidden_layers=[16],
            learning_rate=0.01,
            batch_size=8,
            target_update_frequency=5,
            min_buffer_size=10,
            buffer_size=50,
            discount_factor=0.95
        )
        
        evasion_config = EvasionConfig(
            consistency_threshold=0.9,
            pattern_detection_window=10,
            noise_intensity=0.1,
            technique_weights={
                EvasionTechnique.BEHAVIORAL_NOISE: 1.0
            },
            target_consistency_range=(0.7, 0.85)
        )
        
        adaptation_config = AdaptationConfig(
            performance_window=5,
            performance_threshold=-0.1,
            min_adaptation_interval=3,
            lr_adaptation_enabled=True,
            exploration_adaptation_enabled=True,
            persona_adaptation_enabled=False,
            evasion_adaptation_enabled=True,
            consistency_adaptation_enabled=True,
            detection_risk_threshold=0.8
        )
        
        print("   ✅ Configurations created")
        
        # Create environment
        env = BlackjackRLEnvironment(num_decks=6)
        print("   ✅ Environment created")
        
        # Create agent
        persona = CautiousPersona()
        agent = EvasiveDQNAgent(
            name="Test Agent",
            dqn_config=dqn_config,
            evasion_config=evasion_config,
            adaptation_config=adaptation_config,
            persona=persona
        )
        print("   ✅ Agent created")
        
        # Test episode
        print("\n🎯 Running test episode...")
        
        # Reset environment
        state = env.reset()
        print(f"   📍 Episode started, done: {env.done}")
        
        step_count = 0
        total_reward = 0.0
        
        while not env.done and step_count < 20:  # Safety limit
            step_count += 1
            print(f"   🔄 Step {step_count}")
            
            # Get action from agent
            action = agent.get_action(env.game_state)
            print(f"      Action: {action}")
            
            # Take step
            next_state, reward, done, info = env.step(action)
            print(f"      Reward: {reward.total:.2f}, Done: {done}")
            
            # Update agent
            game_state = info.get('game_state', env.game_state)
            agent.update_experience(reward.total, game_state, done)
            
            # Update tracking
            total_reward += reward.total
            state = next_state
            
            if done:
                print(f"   🏁 Episode completed in {step_count} steps")
                break
        
        print(f"\n📊 Episode Results:")
        print(f"   Steps: {step_count}")
        print(f"   Total Reward: {total_reward:.2f}")
        print(f"   Final Done Status: {env.done}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Episode test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run minimal test."""
    print("🎯 Minimal Phase 3 Training Test")
    print("=" * 40)
    
    start_time = time.time()
    
    success = test_single_episode()
    
    duration = time.time() - start_time
    
    print("\n" + "=" * 40)
    if success:
        print("✅ MINIMAL TEST PASSED")
        print(f"⏱️  Test completed in {duration:.2f} seconds")
        print("🚀 Single episode execution working!")
        return 0
    else:
        print("❌ MINIMAL TEST FAILED")
        print("🔧 Please fix issues before running full training")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
