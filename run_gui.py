#!/usr/bin/env python3
"""
BlackJack Bot ML - Complete GUI Interface Entry Point.

This script launches the comprehensive GUI interface for the BlackJack Bot ML system,
providing both traditional card selection and full interactive blackjack gameplay.

Features:
🎮 DUAL INTERFACE MODES:
- Card Selector Mode: Traditional manual card selection for AI advice
- Interactive Game Mode: Full blackjack gameplay with ML integration

🎯 INTERACTIVE GAME FEATURES:
- Complete blackjack rule implementation (Hit, Stand, Double, Split, Surrender)
- Insurance betting with automatic dealer Ace detection
- Side bets: Perfect Pairs and 21+3 with payout calculations
- Multi-hand display for split hands with visual separation

💰 ADVANCED BANKROLL MANAGEMENT:
- Kelly Criterion betting strategy with confidence-based adjustments
- Real-time bankroll tracking and risk of ruin calculations
- Dynamic bet sizing based on ML model predictions
- Session profit/loss tracking with comprehensive statistics

🤖 ML INTEGRATION:
- Real-time AI advice for all game situations
- Multiple agent types (BasicStrategy, Personas, RL)
- Evasion strategy integration for detection avoidance
- Confidence-based betting recommendations

🔧 PRODUCTION-READY FEATURES:
- Complete rule enforcement (H17, DAS, number of decks, etc.)
- Comprehensive error handling and validation
- Tabbed interface for easy mode switching
- Enhanced help system with detailed feature documentation

Usage:
    python run_gui.py

Requirements:
    - Python 3.8+
    - tkinter (usually included with Python)
    - All BlackJack Bot ML dependencies

Author: Augment Agent
Phase: 4 (GUI Interface)
"""

import sys
import os
import traceback
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    # Check tkinter
    try:
        import tkinter as tk
        from tkinter import ttk
    except ImportError:
        missing_deps.append("tkinter")
    
    # Check core modules
    try:
        from core.card import Card, Suit, Rank
        from core.game_logic import BlackjackGame, GameAction
        from agents.basic_strategy_agent import BasicStrategyAgent
    except ImportError as e:
        missing_deps.append(f"core modules ({str(e)})")
    
    # Check GUI modules
    try:
        from gui.main_window import BlackjackGUI
    except ImportError as e:
        missing_deps.append(f"GUI modules ({str(e)})")
    
    return missing_deps

def show_startup_info():
    """Display startup information."""
    print("=" * 70)
    print("BlackJack Bot ML - Complete GUI Interface")
    print("=" * 70)
    print("🎮 Dual Interface: Card Selection + Interactive Gameplay")
    print("🎯 Complete Blackjack Rules: Hit, Stand, Double, Split, Surrender")
    print("💰 Advanced Bankroll Management with Kelly Criterion")
    print("🤖 ML Integration: Real-time AI advice and confidence betting")
    print("🎲 Side Bets: Perfect Pairs and 21+3 with payout calculations")
    print("📊 Multi-hand Display: Visual separation for split hands")
    print("⚙️  Production-ready: Complete rule enforcement and validation")
    print("🔧 Enhanced Features: Risk management and evasion strategies")
    print("=" * 70)
    print()

def show_help():
    """Display help information."""
    help_text = """
BlackJack Bot ML - Complete GUI Interface Help

🎮 INTERFACE MODES:
The GUI provides two distinct modes accessible via tabs:

1. CARD SELECTOR MODE:
   - Traditional manual card selection for AI advice
   - Select dealer upcard using rank buttons (A, 2-10, J, Q, K)
   - Add player cards by clicking rank buttons multiple times
   - Get instant AI recommendations with confidence levels

2. INTERACTIVE GAME MODE:
   - Full blackjack gameplay with ML integration
   - Start new games with configurable bet amounts
   - Use action buttons: Hit, Stand, Double Down, Split, Surrender
   - Insurance betting when dealer shows Ace
   - Side bets: Perfect Pairs and 21+3

💰 BANKROLL MANAGEMENT:
- Kelly Criterion betting strategy with confidence adjustments
- Real-time bankroll tracking and risk of ruin calculations
- Dynamic bet sizing based on ML model predictions
- Session profit/loss tracking with detailed statistics

🤖 AI AGENTS:
- Basic Strategy: Perfect mathematical strategy (100% accuracy)
- Cautious Persona: Conservative player (95% accuracy)
- Aggressive Persona: Action-oriented player (90% accuracy)
- Intuitive Persona: Emotional player (70% accuracy)
- Dynamic Persona: Switches between personas for detection avoidance
- RL Agent: Deep learning agent with bankroll optimization

🎯 ADVANCED FEATURES:
- Multi-hand display for split hands with visual separation
- Complete rule enforcement (H17, DAS, number of decks, etc.)
- Side bet payout calculations (Perfect Pairs 6:1, 21+3 9:1)
- Evasion strategy integration for detection avoidance
- Real-time game state synchronization

⌨️ KEYBOARD SHORTCUTS:
- Ctrl+R: Reset all selections and game state
- Ctrl+Q: Quit application
- F1: Show help dialog

🔧 CONFIGURATION:
- Game Rules: Adjust number of decks, dealer rules, payouts
- Bankroll: Set initial amount, betting strategy, risk parameters
- AI Agents: Select default agent, confidence display options
- Advanced: Simulation settings, logging, debug mode

📚 DOCUMENTATION:
- TRAINING_OPTIONS.md: Complete training configuration guide
- README.md: Project overview and setup instructions
- In-app help: F1 key for detailed feature explanations

🚀 GETTING STARTED:
1. Choose your preferred mode (Card Selector or Interactive Game)
2. Configure game rules and bankroll settings if needed
3. Select an AI agent for recommendations
4. Start playing and observe ML-powered advice and betting strategies

For technical details and advanced configuration, see the project documentation.
    """
    print(help_text)

def main():
    """Main entry point for the GUI application."""
    try:
        # Show startup information
        show_startup_info()
        
        # Check command line arguments
        if len(sys.argv) > 1:
            if sys.argv[1] in ['-h', '--help', 'help']:
                show_help()
                return 0
            elif sys.argv[1] in ['-v', '--version', 'version']:
                print("BlackJack Bot ML GUI v1.0.0")
                print("Phase 4: GUI Interface Implementation")
                return 0
        
        # Check dependencies
        print("🔍 Checking dependencies...")
        missing_deps = check_dependencies()
        
        if missing_deps:
            print("❌ Missing dependencies:")
            for dep in missing_deps:
                print(f"   - {dep}")
            print("\nPlease install missing dependencies and try again.")
            print("See README.md for installation instructions.")
            return 1
        
        print("✅ All dependencies found")
        print()
        
        # Import GUI after dependency check
        from gui.main_window import BlackjackGUI
        
        # Create and run the GUI application
        print("🚀 Starting BlackJack Bot ML GUI...")
        print("   Close this terminal window to quit the application")
        print("   Use Ctrl+Q in the GUI to quit gracefully")
        print()
        
        # Initialize and run the GUI
        app = BlackjackGUI()
        
        print("✅ GUI initialized successfully")
        print("🎮 Ready for card selection and AI advice!")
        print()
        
        # Start the main event loop
        app.run()
        
        print("👋 BlackJack Bot ML GUI closed")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Application interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Error starting GUI application:")
        print(f"   {str(e)}")
        print("\n🔧 Debug information:")
        traceback.print_exc()
        print("\n💡 Troubleshooting:")
        print("   1. Check that all dependencies are installed")
        print("   2. Verify Python version is 3.8 or higher")
        print("   3. Ensure tkinter is available (python -m tkinter)")
        print("   4. Check project file structure is intact")
        print("   5. See README.md for detailed setup instructions")
        return 1

def run_tests():
    """Run basic GUI tests."""
    print("🧪 Running GUI tests...")
    
    try:
        # Test imports
        from gui.main_window import BlackjackGUI
        from gui.card_selector import CardSelectorWidget
        from gui.game_display import GameDisplayWidget
        from gui.advice_engine import AdviceEngineGUI
        from gui.config_panel import ConfigPanelWidget
        
        print("✅ All GUI modules import successfully")
        
        # Test basic functionality
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide test window
        
        # Test widget creation
        frame = tk.Frame(root)
        card_selector = CardSelectorWidget(frame)
        game_display = GameDisplayWidget(frame)
        advice_engine = AdviceEngineGUI(frame)
        config_panel = ConfigPanelWidget(frame)
        
        print("✅ All GUI widgets create successfully")
        
        root.destroy()
        
        print("✅ GUI tests passed")
        return True
        
    except Exception as e:
        print(f"❌ GUI tests failed: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Handle special commands
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            success = run_tests()
            sys.exit(0 if success else 1)
        elif sys.argv[1] in ['help', '-h', '--help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['version', '-v', '--version']:
            print("BlackJack Bot ML GUI v1.0.0")
            sys.exit(0)
    
    # Run the main application
    exit_code = main()
    sys.exit(exit_code)
