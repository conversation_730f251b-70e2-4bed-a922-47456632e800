"""
Advice Engine GUI Widget for BlackJack Bot ML.

This module integrates with all agent types (BasicStrategy, Personas, RL)
to provide AI-powered blackjack advice through the GUI interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any, Optional, Callable
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.card import Card
from core.hand import Hand
from core.game_logic import BlackjackGame, GameState, GameAction
from agents.basic_strategy_agent import BasicStrategyAgent
from personas.human_persona_agent import HumanPersonaAgent
from personas.cautious_persona import CautiousPersona
from personas.aggressive_persona import AggressivePersona
from personas.intuitive_persona import IntuitivePersona
from personas.persona_switcher_agent import PersonaSwitcherAgent
from personas.persona_switcher import PersonaSwitcher, SwitchConfig


class AdviceEngineGUI:
    """
    GUI widget for AI advice integration.
    
    Provides interface for selecting different AI agents and displaying
    their recommendations with confidence levels and reasoning.
    """
    
    def __init__(self, parent, on_advice_update: Optional[Callable] = None):
        """
        Initialize the advice engine widget.
        
        Args:
            parent: Parent tkinter widget
            on_advice_update: Callback for advice updates
        """
        self.parent = parent
        self.on_advice_update = on_advice_update
        
        # Game engine for advice calculation
        from core.game_rules import get_realistic_rules
        self.game = BlackjackGame(rules=get_realistic_rules())
        
        # Available agents
        self.agents = {}
        self.current_agent_name = None
        self.current_advice = None
        
        # Initialize agents
        self.initialize_agents()
        
        self.create_widgets()
        
    def initialize_agents(self):
        """Initialize all available AI agents."""
        try:
            # Basic Strategy Agent
            self.agents["Basic Strategy"] = BasicStrategyAgent("Basic Strategy")
            
            # Persona Agents
            self.agents["Cautious Persona"] = HumanPersonaAgent(
                "Cautious Player", CautiousPersona()
            )
            self.agents["Aggressive Persona"] = HumanPersonaAgent(
                "Aggressive Player", AggressivePersona()
            )
            self.agents["Intuitive Persona"] = HumanPersonaAgent(
                "Intuitive Player", IntuitivePersona()
            )
            
            # Persona Switcher Agent
            switch_config = SwitchConfig(
                min_hands_per_persona=10,
                max_hands_per_persona=50
            )
            switcher_agent = PersonaSwitcherAgent("Dynamic Player", switch_config)
            switcher_agent.switcher.add_persona("cautious", CautiousPersona())
            switcher_agent.switcher.add_persona("aggressive", AggressivePersona())
            switcher_agent.switcher.add_persona("intuitive", IntuitivePersona())

            self.agents["Dynamic Persona"] = switcher_agent
            
            # Try to load RL agent if available
            try:
                from rl.evasive_dqn_agent import EvasiveDQNAgent
                from rl.dqn_agent import DQNConfig
                from rl.evasion_strategies import EvasionConfig
                from rl.adaptive_learning import AdaptationConfig
                
                # Create minimal RL agent configuration
                dqn_config = DQNConfig(hidden_layers=[64, 32])
                evasion_config = EvasionConfig()
                adaptation_config = AdaptationConfig()
                
                # Create a simple persona switcher for RL agent
                rl_switch_config = SwitchConfig(min_hands_per_persona=5, max_hands_per_persona=20)
                rl_persona_switcher = PersonaSwitcher(rl_switch_config)
                rl_persona_switcher.add_persona("cautious", CautiousPersona())
                rl_persona_switcher.add_persona("aggressive", AggressivePersona())

                rl_agent = EvasiveDQNAgent(
                    "RL Agent",
                    dqn_config,
                    evasion_config,
                    adaptation_config,
                    persona_switcher=rl_persona_switcher
                )
                self.agents["RL Agent (Untrained)"] = rl_agent
                
            except Exception as e:
                print(f"RL agent not available: {e}")
                
        except Exception as e:
            print(f"Error initializing agents: {e}")
            # Fallback to basic strategy only
            self.agents = {"Basic Strategy": BasicStrategyAgent("Basic Strategy")}
            
        # Set default agent
        if self.agents:
            self.current_agent_name = list(self.agents.keys())[0]
            
    def create_widgets(self):
        """Create all advice engine widgets."""
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        
        # Agent selection section
        self.create_agent_selection()
        
        # Advice display section
        self.create_advice_display()
        
        # Action buttons section
        self.create_action_buttons()
        
    def create_agent_selection(self):
        """Create agent selection interface."""
        self.agent_frame = ttk.LabelFrame(
            self.main_frame,
            text="AI Agent Selection",
            padding="10"
        )
        
        # Agent selection label
        self.agent_label = ttk.Label(
            self.agent_frame,
            text="Choose AI Agent:",
            font=('Arial', 10, 'bold')
        )
        
        # Agent selection combobox
        self.agent_var = tk.StringVar()
        self.agent_combobox = ttk.Combobox(
            self.agent_frame,
            textvariable=self.agent_var,
            values=list(self.agents.keys()),
            state="readonly",
            width=20
        )
        
        if self.current_agent_name:
            self.agent_var.set(self.current_agent_name)
            
        self.agent_combobox.bind('<<ComboboxSelected>>', self.on_agent_change)
        
        # Agent description
        self.agent_description = ttk.Label(
            self.agent_frame,
            text="",
            font=('Arial', 9),
            foreground='gray',
            wraplength=250
        )
        
        self.update_agent_description()
        
    def create_advice_display(self):
        """Create advice display interface."""
        self.advice_frame = ttk.LabelFrame(
            self.main_frame,
            text="AI Recommendation",
            padding="10"
        )
        
        # Recommended action display
        self.action_label = ttk.Label(
            self.advice_frame,
            text="Action:",
            font=('Arial', 10, 'bold')
        )
        
        self.action_value = ttk.Label(
            self.advice_frame,
            text="Select cards for advice",
            font=('Arial', 14, 'bold'),
            foreground='blue'
        )
        
        # Confidence display
        self.confidence_label = ttk.Label(
            self.advice_frame,
            text="Confidence:",
            font=('Arial', 10, 'bold')
        )
        
        self.confidence_value = ttk.Label(
            self.advice_frame,
            text="-",
            font=('Arial', 12),
            foreground='green'
        )
        
        # Reasoning display
        self.reasoning_label = ttk.Label(
            self.advice_frame,
            text="Reasoning:",
            font=('Arial', 10, 'bold')
        )
        
        self.reasoning_text = tk.Text(
            self.advice_frame,
            height=4,
            width=30,
            wrap=tk.WORD,
            font=('Arial', 9),
            state=tk.DISABLED,
            bg='#f8f8f8',
            relief=tk.FLAT,
            borderwidth=1
        )
        
        # Alternative actions display
        self.alternatives_label = ttk.Label(
            self.advice_frame,
            text="Alternative Actions:",
            font=('Arial', 10, 'bold')
        )
        
        self.alternatives_frame = ttk.Frame(self.advice_frame)
        
    def create_action_buttons(self):
        """Create action control buttons."""
        self.buttons_frame = ttk.Frame(self.main_frame)
        
        # Refresh advice button
        self.refresh_btn = ttk.Button(
            self.buttons_frame,
            text="Refresh Advice",
            command=self.refresh_advice,
            style='Action.TButton'
        )
        
        # Compare agents button
        self.compare_btn = ttk.Button(
            self.buttons_frame,
            text="Compare Agents",
            command=self.compare_agents,
            style='Action.TButton'
        )
        
        # Clear advice button
        self.clear_btn = ttk.Button(
            self.buttons_frame,
            text="Clear",
            command=self.clear_advice,
            style='Action.TButton'
        )
        
    def setup_layout(self):
        """Set up the layout of all widgets."""
        # Main frame
        self.main_frame.grid(row=0, column=0, sticky='nsew')
        
        # Configure grid weights
        self.parent.grid_rowconfigure(0, weight=1)
        self.parent.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # Agent selection section
        self.agent_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))
        self.agent_frame.grid_columnconfigure(0, weight=1)
        
        self.agent_label.grid(row=0, column=0, sticky='w', pady=(0, 5))
        self.agent_combobox.grid(row=1, column=0, sticky='ew', pady=(0, 5))
        self.agent_description.grid(row=2, column=0, sticky='w')
        
        # Advice display section
        self.advice_frame.grid(row=1, column=0, sticky='nsew', pady=(0, 10))
        self.advice_frame.grid_columnconfigure(0, weight=1)
        self.advice_frame.grid_rowconfigure(4, weight=1)
        
        self.action_label.grid(row=0, column=0, sticky='w', pady=(0, 2))
        self.action_value.grid(row=0, column=1, sticky='w', padx=(10, 0), pady=(0, 2))
        
        self.confidence_label.grid(row=1, column=0, sticky='w', pady=(0, 2))
        self.confidence_value.grid(row=1, column=1, sticky='w', padx=(10, 0), pady=(0, 2))
        
        self.reasoning_label.grid(row=2, column=0, sticky='nw', pady=(5, 2))
        self.reasoning_text.grid(row=3, column=0, columnspan=2, sticky='ew', pady=(0, 5))
        
        self.alternatives_label.grid(row=4, column=0, sticky='nw', pady=(5, 2))
        self.alternatives_frame.grid(row=5, column=0, columnspan=2, sticky='ew')
        
        # Action buttons section
        self.buttons_frame.grid(row=2, column=0, sticky='ew')
        self.buttons_frame.grid_columnconfigure(0, weight=1)
        self.buttons_frame.grid_columnconfigure(1, weight=1)
        self.buttons_frame.grid_columnconfigure(2, weight=1)
        
        self.refresh_btn.grid(row=0, column=0, padx=(0, 5), pady=5, sticky='ew')
        self.compare_btn.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        self.clear_btn.grid(row=0, column=2, padx=(5, 0), pady=5, sticky='ew')
        
    def on_agent_change(self, event=None):
        """Handle agent selection change."""
        self.current_agent_name = self.agent_var.get()
        self.update_agent_description()
        self.clear_advice()
        
    def update_agent_description(self):
        """Update agent description text."""
        descriptions = {
            "Basic Strategy": "Perfect mathematical strategy with 100% accuracy",
            "Cautious Persona": "Conservative player with 95% accuracy, slower decisions",
            "Aggressive Persona": "Action-oriented player with 90% accuracy, faster decisions", 
            "Intuitive Persona": "Emotional player with 70% accuracy, variable timing",
            "Dynamic Persona": "Switches between personas to avoid detection",
            "RL Agent (Untrained)": "Deep learning agent (requires training for optimal performance)"
        }
        
        description = descriptions.get(self.current_agent_name, "AI agent")
        self.agent_description.config(text=description)
        
    def get_advice(self, dealer_cards: List[Card], player_cards: List[Card]):
        """
        Get AI advice for the given cards.
        
        Args:
            dealer_cards: Dealer's cards
            player_cards: Player's cards
        """
        if not dealer_cards or not player_cards:
            self.clear_advice()
            return
            
        try:
            # Create game state
            game_state = self.create_game_state(dealer_cards, player_cards)
            
            # Get current agent
            agent = self.agents.get(self.current_agent_name)
            if not agent:
                self.show_error("No agent selected")
                return
                
            # Get action recommendation
            action = agent.get_action(game_state, hand_index=0)
            
            # Calculate confidence and reasoning
            confidence = self.calculate_confidence(agent, game_state)
            reasoning = self.generate_reasoning(agent, game_state, action)
            alternatives = self.get_alternative_actions(game_state)
            
            # Update display
            self.display_advice(action, confidence, reasoning, alternatives)
            
            # Notify callback
            if self.on_advice_update:
                advice_data = {
                    'action': action.value,
                    'confidence': confidence,
                    'reasoning': reasoning,
                    'agent': self.current_agent_name
                }
                self.on_advice_update(advice_data)
                
        except Exception as e:
            self.show_error(f"Error getting advice: {str(e)}")
            
    def create_game_state(self, dealer_cards: List[Card], player_cards: List[Card]) -> GameState:
        """Create a GameState object from the selected cards."""
        # Start new game
        game_state = self.game.start_new_game(bet_amount=10.0)
        
        # Replace cards with selected cards
        # Clear existing cards
        game_state.dealer_hand.cards = []
        game_state.player_hands[0].cards = []
        
        # Add selected cards
        for card in dealer_cards:
            game_state.dealer_hand.add_card(card)
            
        for card in player_cards:
            game_state.player_hands[0].add_card(card)
            
        # Update action availability
        player_hand = game_state.player_hands[0]
        game_state.can_double = [player_hand.can_double()]
        game_state.can_split = [player_hand.can_split()]
        
        return game_state
        
    def calculate_confidence(self, agent, game_state: GameState) -> float:
        """Calculate confidence level for the advice."""
        # Basic confidence calculation based on agent type
        if self.current_agent_name == "Basic Strategy":
            return 1.0  # Perfect strategy
        elif "Persona" in self.current_agent_name:
            # Get persona accuracy
            if hasattr(agent, 'persona'):
                return agent.persona.config.accuracy
            else:
                return 0.85  # Default persona accuracy
        else:
            return 0.75  # Default for other agents
            
    def generate_reasoning(self, agent, game_state: GameState, action: GameAction) -> str:
        """Generate reasoning text for the recommendation."""
        player_hand = game_state.player_hands[0]
        dealer_upcard = game_state.dealer_hand.cards[0] if game_state.dealer_hand.cards else None
        
        reasoning_parts = []
        
        # Basic situation description
        player_value = player_hand.get_value()
        dealer_value = dealer_upcard.get_value() if dealer_upcard else 0
        
        reasoning_parts.append(f"Player has {player_value}, dealer shows {dealer_value}.")
        
        # Action-specific reasoning
        if action == GameAction.HIT:
            reasoning_parts.append("Taking another card to improve hand value.")
        elif action == GameAction.STAND:
            reasoning_parts.append("Standing to avoid risk of busting.")
        elif action == GameAction.DOUBLE:
            reasoning_parts.append("Doubling down for maximum value in favorable situation.")
        elif action == GameAction.SPLIT:
            reasoning_parts.append("Splitting pair to create two separate hands.")
            
        # Agent-specific reasoning
        if self.current_agent_name == "Basic Strategy":
            reasoning_parts.append("Based on mathematically optimal Basic Strategy.")
        elif "Cautious" in self.current_agent_name:
            reasoning_parts.append("Conservative approach minimizing risk.")
        elif "Aggressive" in self.current_agent_name:
            reasoning_parts.append("Aggressive play maximizing potential gains.")
        elif "Intuitive" in self.current_agent_name:
            reasoning_parts.append("Decision based on intuition and feel.")
            
        return " ".join(reasoning_parts)
        
    def get_alternative_actions(self, game_state: GameState) -> List[GameAction]:
        """Get list of alternative actions."""
        available_actions = self.game.get_available_actions()
        return [action for action in available_actions if action != self.current_advice]
        
    def display_advice(self, action: GameAction, confidence: float, reasoning: str, alternatives: List[GameAction]):
        """Display the advice in the GUI."""
        # Update action display
        action_text = action.value.upper()
        action_color = self.get_action_color(action)
        self.action_value.config(text=action_text, foreground=action_color)
        
        # Update confidence display
        confidence_text = f"{confidence:.1%}"
        confidence_color = self.get_confidence_color(confidence)
        self.confidence_value.config(text=confidence_text, foreground=confidence_color)
        
        # Update reasoning display
        self.reasoning_text.config(state=tk.NORMAL)
        self.reasoning_text.delete(1.0, tk.END)
        self.reasoning_text.insert(tk.END, reasoning)
        self.reasoning_text.config(state=tk.DISABLED)
        
        # Update alternatives display
        self.update_alternatives_display(alternatives)
        
        # Store current advice
        self.current_advice = action
        
    def get_action_color(self, action: GameAction) -> str:
        """Get color for action display."""
        colors = {
            GameAction.HIT: "blue",
            GameAction.STAND: "green", 
            GameAction.DOUBLE: "orange",
            GameAction.SPLIT: "purple"
        }
        return colors.get(action, "black")
        
    def get_confidence_color(self, confidence: float) -> str:
        """Get color for confidence display."""
        if confidence >= 0.9:
            return "green"
        elif confidence >= 0.7:
            return "orange"
        else:
            return "red"
            
    def update_alternatives_display(self, alternatives: List[GameAction]):
        """Update the alternatives display."""
        # Clear existing alternatives
        for widget in self.alternatives_frame.winfo_children():
            widget.destroy()
            
        # Add alternative action buttons
        for i, action in enumerate(alternatives):
            btn = ttk.Button(
                self.alternatives_frame,
                text=action.value.upper(),
                width=8,
                command=lambda a=action: self.show_alternative_info(a)
            )
            btn.grid(row=0, column=i, padx=2)
            
    def show_alternative_info(self, action: GameAction):
        """Show information about an alternative action."""
        info_text = f"Alternative action: {action.value.upper()}\n\nThis action is available but not recommended by the current AI agent."
        messagebox.showinfo("Alternative Action", info_text)
        
    def refresh_advice(self):
        """Refresh the current advice."""
        # This would be called by the parent to refresh with current cards
        pass
        
    def compare_agents(self):
        """Show comparison of different agents."""
        messagebox.showinfo("Compare Agents", "Agent comparison feature coming soon!")
        
    def clear_advice(self):
        """Clear the advice display."""
        self.action_value.config(text="Select cards for advice", foreground="gray")
        self.confidence_value.config(text="-", foreground="gray")
        
        self.reasoning_text.config(state=tk.NORMAL)
        self.reasoning_text.delete(1.0, tk.END)
        self.reasoning_text.insert(tk.END, "Select dealer and player cards to get AI advice.")
        self.reasoning_text.config(state=tk.DISABLED)
        
        # Clear alternatives
        for widget in self.alternatives_frame.winfo_children():
            widget.destroy()
            
        self.current_advice = None
        
    def update_config(self, config_data: Dict[str, Any]):
        """Update configuration settings."""
        # This would update agent configurations based on settings
        pass
        
    def show_error(self, message: str):
        """Show an error message."""
        messagebox.showerror("Advice Engine Error", message)
